import BasicLayoutV1 from "@/layouts/BasicLayoutV1.vue"
import { RouteRecordRaw } from "vue-router"
// 默认路由
const constantRoutes: Array<RouteRecordRaw> = [
    {
        path: "/user",
        name: "user",
        component: () => import("@/views/user/index.vue"),
        redirect: "/user/login",
        children: [
            {
                path: "login",
                name: "login",
                component: () => import("@/views/user/login.vue"),
                meta: {
                    title: "登录"
                }
            },
            {
                path: "register",
                name: "register",
                component: () => import("@/views/user/register.vue"),
                meta: {
                    title: "注册"
                }
            },
            {
                path: "/error",
                name: "error",
                meta: {
                    title: "error"
                },
                component: () => import("@/views/error/index.vue"),
                redirect: "/error/404",
                children: [
                    {
                        path: "404",
                        name: "NotFound",
                        component: () => import("@/views/error/404.vue"),
                        meta: {
                            title: "资源不存在"
                        }
                    },
                    {
                        path: "401",
                        name: "unauthorized",
                        component: () => import("@/views/error/401.vue"),
                        meta: {
                            title: "没有访问权限"
                        }
                    }
                ]
            }
        ]
    }
]
// 异步路由
const asyncRouterMap: Array<RouteRecordRaw> = [
    {
        path: "/",
        name: "index",
        component: BasicLayoutV1,
        meta: {
            title: "商户管理"
        },
        redirect: "/merchantManage",
        children: [
            {
                path: "/merchantManage",
                name: "merchantManage",
                meta: {
                    title: "商户管理",
                    icon: "home-outlined"
                },
                component: () => import("@/views/merchantManage/index.vue"),
                children: [
                    {
                        path: "/merchantManage/moneyConfig",
                        name: "moneyConfig",
                        meta: {
                            title: "微信收款配置",
                            icon: "home-outlined",
                            hidden: true
                        },
                        hideInMenu: true,
                        component: () => import("@/views/merchantManage/moneyConfig/index.vue")
                    }
                ]
            },
            {
                path: "/payManage",
                name: "payManage",
                meta: {
                    title: "支付管理",
                    icon: "home-outlined"
                },
                component: () => import("@/views/payManage/index.vue"),
                redirect: "/payManage/cardManage",
                children: [
                    {
                        path: "/payManage/cardManage",
                        name: "cardManage",
                        meta: {
                            title: "卡片管理",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/payManage/cardManage/index.vue"),
                        children: [
                            {
                                path: "/payManage/cardManage/cardNumber",
                                name: "cardNumber",
                                meta: {
                                    title: "卡号管理",
                                    icon: "home-outlined",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/payManage/cardManage/cardNumber/index.vue")
                            },
                            {
                                path: "/payManage/cardManage/cardOperate",
                                name: "cardOperate",
                                meta: {
                                    title: "卡号操作",
                                    icon: "home-outlined",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/payManage/cardManage/cardOperate/index.vue")
                            }
                        ]
                    },
                    {
                        path: "/payManage/faceManage",
                        name: "faceManage",
                        meta: {
                            title: "人脸管理",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/payManage/faceManage/index.vue")
                    },
                    {
                        path: "/payManage/dataSync",
                        name: "dataSync",
                        meta: {
                            title: "数据同步",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/payManage/dataSync/index.vue")
                    }
                ]
            },
            {
                path: "/rechargeMange",
                name: "rechargeMange",
                meta: {
                    title: "充值管理",
                    icon: "home-outlined"
                },
                component: () => import("@/views/rechargeMange/index.vue"),
                redirect: "/rechargeMange/rechargeRecord",
                children: [
                    {
                        path: "/rechargeMange/rechargeRecord",
                        name: "rechargeRecord",
                        meta: {
                            title: "充值记录",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/rechargeMange/rechargeRecord/index.vue")
                    },
                    {
                        path: "/rechargeMange/refundRecord",
                        name: "refundRecord",
                        meta: {
                            title: "退款记录",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/rechargeMange/refundRecord/index.vue")
                    }
                ]
            },
            {
                path: "/consumptionManage",
                name: "consumptionManage",
                meta: {
                    title: "消费管理",
                    icon: "home-outlined"
                },
                component: () => import("@/views/consumptionManage/index.vue"),
                redirect: "/consumptionManage/consumptionDeploy",
                children: [
                    {
                        path: "/consumptionManage/consumptionDeploy",
                        name: "consumptionDeploy",
                        meta: {
                            title: "消费配置",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/consumptionManage/consumptionDeploy/index.vue")
                    },
                    {
                        path: "/consumptionManage/order",
                        name: "order",
                        meta: {
                            title: "充值订单",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/consumptionManage/order/index.vue"),
                        redirect: "/consumptionManage/order/rechargeOrder",
                        children: [
                            {
                                path: "/consumptionManage/order/rechargeOrder",
                                name: "rechargeOrder",
                                meta: {
                                    title: "充值订单",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/consumptionManage/order/rechargeOrder/index.vue")
                            },
                            {
                                path: "/consumptionManage/order/refundOrder",
                                name: "refundOrder",
                                meta: {
                                    title: "退款订单",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/consumptionManage/order/refundOrder/index.vue")
                            }
                        ]
                    },
                    {
                        path: "/consumptionManage/canteenOrder",
                        name: "canteenOrder",
                        meta: {
                            title: "食堂消费订单",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/consumptionManage/canteenOrder/index.vue"),
                        redirect: "/consumptionManage/canteenOrder/canteenConsumeOrder",
                        children: [
                            {
                                path: "/consumptionManage/canteenOrder/canteenConsumeOrder",
                                name: "canteenConsumeOrder",
                                meta: {
                                    title: "食堂消费订单",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/consumptionManage/canteenOrder/canteenConsumeOrder/index.vue")
                            },
                            {
                                path: "/consumptionManage/canteenOrder/canteenRefundOrder",
                                name: "canteenRefundOrder",
                                meta: {
                                    title: "退款订单",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/consumptionManage/canteenOrder/canteenRefundOrder/index.vue")
                            }
                        ]
                    },
                    {
                        path: "/consumptionManage/marketOrder",
                        name: "marketOrder",
                        meta: {
                            title: "超市消费订单",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/consumptionManage/marketOrder/index.vue"),
                        redirect: "/consumptionManage/marketOrder/marketConsumeOrder",
                        children: [
                            {
                                path: "/consumptionManage/marketOrder/marketConsumeOrder",
                                name: "marketConsumeOrder",
                                meta: {
                                    title: "超市消费订单",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/consumptionManage/marketOrder/marketConsumeOrder/index.vue")
                            },
                            {
                                path: "/consumptionManage/marketOrder/marketRefundOrder",
                                name: "marketRefundOrder",
                                meta: {
                                    title: "退款订单",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/consumptionManage/marketOrder/marketRefundOrder/index.vue")
                            }
                        ]
                    }
                ]
            },
            {
                path: "/deviceManage",
                name: "deviceManage",
                meta: {
                    title: "设备管理",
                    icon: "home-outlined"
                },
                component: () => import("@/views/deviceManage/index.vue"),
                redirect: "/deviceManage/groupMealMachine",
                children: [
                    {
                        path: "/deviceManage/groupMealMachine",
                        name: "groupMealMachine",
                        meta: {
                            title: "消费机",
                            hidden: true
                        },
                        hideInMenu: true,
                        component: () => import("@/views/deviceManage/groupMealMachine/index.vue"),
                        children: [
                            {
                                path: "/deviceManage/groupMealMachine/edit",
                                name: "editGroupMeal",
                                meta: {
                                    title: "编辑消费机",
                                    icon: "home-outlined",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/deviceManage/groupMealMachine/editDevice/index.vue")
                            }
                        ]
                    },
                    {
                        path: "/deviceManage/cashRegister",
                        name: "cashRegister",
                        meta: {
                            title: "收银机",
                            hidden: true
                        },
                        hideInMenu: true,
                        component: () => import("@/views/deviceManage/cashRegister/index.vue"),
                        children: [
                            {
                                path: "/deviceManage/cashRegister/edit",
                                name: "editCashRegister",
                                meta: {
                                    title: "编辑收银机",
                                    icon: "home-outlined",
                                    hidden: true
                                },
                                hideInMenu: true,
                                component: () => import("@/views/deviceManage/cashRegister/editDevice/index.vue")
                            }
                        ]
                    }
                ]
            },
            {
                path: "/settlementManage",
                name: "settlementManage",
                meta: {
                    title: "结算管理",
                    icon: "home-outlined"
                },
                component: () => import("@/views/settlementManage/index.vue"),
                redirect: "/settlementManage/merchantSettlement",
                children: [
                    {
                        path: "/settlementManage/merchantSettlement",
                        name: "merchantSettlement",
                        meta: {
                            title: "商户结算报表",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/settlementManage/merchantSettlement/index.vue")
                    },
                    {
                        path: "/settlementManage/deviceSettlement",
                        name: "deviceSettlement",
                        meta: {
                            title: "设备结算报表",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/settlementManage/deviceSettlement/index.vue")
                    }
                ]
            },
            // 商户端-商品管理
            {
                path: "/commodityManagement",
                name: "commodityManagement",
                meta: {
                    title: "商品管理",
                    icon: "home-outlined"
                },
                children: [
                    {
                        path: "/commodityManagement/commodityList",
                        name: "commodityList",
                        meta: {
                            title: "商品列表",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/commodityManagement/commodityList/index.vue")
                    },
                    {
                        path: "/commodityManagement/stockManagement",
                        name: "stockManagement",
                        meta: {
                            title: "库存管理",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/commodityManagement/stockManagement/index.vue")
                    },
                    {
                        path: "/commodityManagement/warehouse",
                        name: "warehouse",
                        meta: {
                            title: "入库管理",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/commodityManagement/warehouse/index.vue")
                    },
                    {
                        path: "/commodityManagement/salesRecord",
                        name: "salesRecord",
                        meta: {
                            title: "销售记录",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/commodityManagement/salesRecord/index.vue")
                    },


                ]
            },
            {
                path: "/consumptionManagement",
                name: "consumptionManagement",
                meta: {
                    title: "消费管理",
                    icon: "home-outlined"
                },
                children: [
                    {
                        path: "/consumptionManagement/consumptionList",
                        name: "consumptionList",
                        meta: {
                            title: "超市消费订单",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/consumptionManagement/consumptionList/index.vue")
                    },
                ]
            },
            {
                path: "/settlementManagement",
                name: "settlementManagement",
                meta: {
                    title: "结算管理",
                    icon: "home-outlined"
                },
                children: [
                    {
                        path: "/settlementManagement/merchant",
                        name: "merchant",
                        meta: {
                            title: "商户结算报表",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/settlementManagement/merchant/index.vue")
                    },
                    {
                        path: "/settlementManagement/equipment",
                        name: "equipment",
                        meta: {
                            title: "设备结算报表",
                            icon: "home-outlined"
                        },
                        component: () => import("@/views/settlementManagement/equipment/index.vue")
                    },
                ]
            },
            // 捕获所有路由或 404 Not found 路由
            {
                path: "/:path(.*)*",
                hideInMenu: true,
                name: "any",
                redirect: "/error/404"
            }
        ]
    }
]

// 根级菜单
const rootRouter: RouteRecordRaw = {
    name: "index",
    path: "/",
    component: BasicLayoutV1,
    redirect: "/dashboard",
    meta: {
        title: "首页"
    },
    children: [
        {
            path: "/:path(.*)*",
            hideInMenu: true,
            name: "any",
            redirect: "/error/404"
        }
    ]
}

export { constantRoutes, asyncRouterMap, rootRouter }
