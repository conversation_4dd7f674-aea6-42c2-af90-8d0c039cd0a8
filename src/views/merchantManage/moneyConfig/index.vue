<template>
    <div class="money_config">
        <div class="head">
            <ArrowLeftOutlined @click="back" :style="{ color: 'var(--primary-color)', fontSize: '16px' }" />
            <span class="page_title">收款配置</span>
        </div>
        <div class="content">
            <a-tabs v-model:activeKey="activeKey">
                <a-tab-pane :tab="payTypeName[item.payType]" v-for="item in payList" :key="item.typeKey"> </a-tab-pane>
            </a-tabs>
            <component :info="formInfo" :is="typeKeyCom[activeKey]" @cancelDrawer="back"></component>
        </div>
    </div>
</template>

<script setup>
import WechatConfig from "../components/wechatConfig.vue"

import { useRouter } from "vue-router"
const router = useRouter()
const activeKey = ref("wechat")
const payList = ref([])
const formInfo = ref({})
const payTypeName = ref({
    1: "微信支付",
    2: "支付宝"
})

const typeKeyCom = ref({
    wechat: shallowRef(WechatConfig),
    alipay: 2 // 暂定
})
function getPayList() {
    http.get("/unicard/mgmt/pay-method/list").then((res) => {
        payList.value = res?.data
    })
}

function back() {
    router.back()
}
function getWxInfoFn() {
    http.get("/unicard/mgmt/wx-pay-config/get").then((res) => {
        formInfo.value = res.data || {}
    })
}

onMounted(() => {
    getPayList()
    if (activeKey.value === "wechat") {
        getWxInfoFn()
    }
})
</script>

<style lang="less" scoped>
.money_config {
    .head {
        padding: 18px 20px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
        flex: 1;
        .page_title {
            margin-left: 10px;
            font-weight: 600;
            font-size: 18px;
            color: var(--text-color);
            line-height: 25px;
        }
    }
    .content {
        padding: 14px 20px;
    }
}
</style>
