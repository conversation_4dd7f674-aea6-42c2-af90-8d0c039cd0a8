<template>
    <div>
        <div class="merchant_manage" v-if="route.path === '/merchantManage'">
            <div class="header">商户管理</div>
            <div class="content">
                <search-form v-model:formState="query" :formList="formList" @submit="getInitList" layout="horizontal" @reset="reset">
                    <template #areaList>
                        <a-cascader v-model:value="query.areaList" :field-names="{ label: 'name', value: 'name', children: 'area' }" :options="areaOptions" placeholder="请选择" />
                    </template>
                </search-form>
                <div class="btn_group">
                    <a-button type="primary" @click="addDrawerFn('add', null)">
                        <template #icon>
                            <PlusOutlined />
                        </template>
                        新增商户
                    </a-button>
                    <a-button type="primary" ghost @click="moneyConfig">收款配置</a-button>
                </div>
                <ETable :columns="columns" :scroll="{ x: 1500 }" :data-source="dataSource" @expand="handleExpand" v-model:expandedRowKeys="expandedRowKeys" @change="handleTableChange" :loading="tableLoading">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'merchantType'">
                            {{ merchantTypeText[record.merchantType] || "-" }}
                        </template>
                        <template v-else-if="column.dataIndex === 'status'">
                            {{ record.status === 1 ? "启用" : "禁用" }}
                        </template>
                        <template v-else-if="column.dataIndex === 'operate'">
                            <a-button type="link" class="btn-link-color" @click="infoDrawerFn(record)">详情</a-button>
                            <a-button type="link" class="btn-link-color" @click="addDrawerFn('edit', record)">编辑</a-button>
                            <a-button type="link" class="btn-link-color" @click="statusDeviceFn(record)">{{ record.status === 1 ? "禁用" : "启用" }}</a-button>
                            <a-button type="link" class="btn-link-color" @click="lookDeviceFn(record)">查看设备</a-button>
                            <a-button type="link" class="btn-link-color" v-if="record.pid !== '0'" @click="merAdminFn(record)">商户管理员</a-button>
                        </template>
                    </template>
                </ETable>
            </div>

            <!-- 新增编辑商户 -->
            <AddMerDrawer ref="addMerDrawerRef" @submitDrawer="reset" />

            <!-- 商户详情 -->
            <MerInfoDrawer ref="merInfoDrawerRef" />

            <!-- 商户管理员 -->
            <MerchantAdmin ref="merAdminRef" />
        </div>
        <router-view></router-view>
    </div>
</template>

<script setup>
import MerchantAdmin from "./components/merchantAdmin.vue"
import MerInfoDrawer from "./components/merInfoDrawer.vue" // 商户详情
import AddMerDrawer from "./components/addMerDrawer.vue" // 新增商户
import { message } from "ant-design-vue"
import { useRouter, useRoute } from "vue-router"

const router = useRouter()
const route = useRoute()

const query = ref({})
const dataSource = ref([])
const tableLoading = ref(false)
const areaOptions = ref([])
const expandedRowKeys = ref([])

const merchantTypeText = {
    1: "一级商户",
    2: "二级商户",
    3: "三级商户",
    4: "四级商户"
}

const formList = ref([
    {
        type: "input",
        value: "merchantNo",
        label: "商户ID"
    },
    {
        type: "input",
        value: "merchantName",
        label: "商户名称"
    },
    {
        type: "slot",
        value: "areaList",
        label: "所在地址"
    },
    {
        type: "select",
        value: "merchantType",
        label: "商户类型",
        list: [
            { label: "全部", value: null },
            { label: "一级商户", value: 1 },
            { label: "二级商户", value: 2 },
            { label: "门店", value: 3 },
            { label: "个人", value: 4 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "select",
        value: "status",
        label: "商户状态",
        list: [
            { label: "全部", value: null },
            { label: "启用", value: 1 },
            { label: "禁用", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "input",
        value: "headName",
        label: "负责人"
    },
    {
        type: "rangePicker",
        value: ["startUpdateDate", "endUpdateDate"],
        label: "更新时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const columns = ref([
    { title: "商户ID", dataIndex: "merchantNo", key: "merchantNo", width: 100 },
    { title: "商户名称", dataIndex: "merchantName", key: "merchantName", width: 150 },
    { title: "所在地", dataIndex: "areaName", key: "areaName", width: 200 },
    { title: "商户类型", dataIndex: "merchantType", key: "merchantType", width: 100 },
    { title: "商户状态", dataIndex: "status", key: "status", width: 100 },
    { title: "更新时间", dataIndex: "updateTime", key: "updateTime", width: 160 },
    { title: "操作", dataIndex: "operate", width: 210, fixed: "right" }
])

const addMerDrawerRef = ref(null)
const merInfoDrawerRef = ref(null)
const merAdminRef = ref(null)

// 收款配置
function moneyConfig() {
    router.push({
        path: "/merchantManage/moneyConfig"
    })
}

// 新增编辑
function addDrawerFn(type, record = {}) {
    addMerDrawerRef.value.open(type, deepClone(record), areaOptions.value)
}

function getInfo(id) {
    http.post("/unicard/mgmt/merchant-manage/get", { id }).then((res) => {
        merInfoDrawerRef.value.open(res.data)
    })
}

// 详情
function infoDrawerFn(record) {
    getInfo(record.id)
}

// 商户状态
function statusDeviceFn(item) {
    http.post("/unicard/mgmt/merchant-manage/updateStatus", { id: item.id, status: item.status === 1 ? 0 : 1 }).then((res) => {
        message.success(res.message)
        reset()
    })
}

// 查看设备
function lookDeviceFn() {
    console.log("查看设备")
    router.push({
        path: "/deviceManage/groupMealMachine"
    })
}

// 商户管理员
function merAdminFn(item) {
    merAdminRef.value.open(item)
}
// 获取地区树形列表
function getAreaList() {
    http.get("/unicard/mgmt/merchant-manage/areaList").then((res) => {
        areaOptions.value = res.data
    })
}

function getList() {
    query.value.areaName = query.value?.areaList?.join("/") || ""
    tableLoading.value = true
    http.post("/unicard/mgmt/merchant-manage/list", query.value)
        .then((res) => {
            dataSource.value = res.data
        })
        .finally(() => {
            tableLoading.value = false
        })
}

// 点击展开事件
const handleExpand = async (expanded, record) => {
    if (!expanded) return
    const res = await http.post("/unicard/mgmt/merchant-manage/list", { pid: record.id })
    record.children = res.data
}

function handleTableChange() {
    expandedRowKeys.value = []
}

function getInitList() {
    getList()
}

function reset() {
    query.value = {}
    expandedRowKeys.value = []
    getList()
}

onMounted(() => {
    getAreaList()
    reset()
})
</script>

<style lang="less" scoped>
.merchant_manage {
    .header {
        padding: 18px 20px;
        font-weight: 500;
        font-size: 18px;
        color: #262626;
        line-height: 25px;
        border-bottom: 1px solid #d9d9d9;
    }

    .content {
        padding: 20px;

        .btn_group {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 16px;
        }
    }
}
</style>
