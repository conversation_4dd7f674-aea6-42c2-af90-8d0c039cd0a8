<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="商户详情" @close="cancel" :footer="null">
            <div style="margin-top: 20px">
                <ETable :columns="columns" :data-source="dataSource" :paginations="pagination" @change="handleTableChange" :loading="tableLoading">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex == 'isEnabled'">
                            {{ record.isEnabled ? "禁用" : "启用" }}
                        </template>
                        <template v-if="column.dataIndex == 'operate'">
                            <a-button type="link" class="btn-link-color" @click="statusAdmin(record)">{{ !record.isEnabled ? "禁用" : "启用" }}</a-button>
                            <a-button type="link" class="btn-link-color" v-if="record.username" :loading="resetLoading" @click="resetPassword(record.id)">重置密码</a-button>
                        </template>
                    </template>
                </ETable>
            </div>
        </YDrawer>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const drawerOpen = ref(false)
const tableLoading = ref(false)
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const resetLoading = ref(false)
const merInfo = ref({})

const columns = ref([
    { title: "ID", dataIndex: "id" },
    { title: "管理员", dataIndex: "name", key: "name" },
    { title: "账号", dataIndex: "username", key: "username" },
    { title: "帐号状态", dataIndex: "isEnabled", key: "isEnabled" },
    { title: "更新时间", dataIndex: "updateTime", key: "updateTime" },
    { title: "操作", dataIndex: "operate", key: "operate", width: 220, fixed: "right" }
])

// 修改状态
function statusAdmin(item) {
    http.post("/system/adminUser/updateLocked", { id: item.id, isLocked: !item.isEnabled }).then((res) => {
        message.success(res.message)
        getInitList()
    })
}

// 重置密码
function resetPassword(id) {
    resetLoading.value = true
    http.get("/system/adminUser/resetPassword", { id })
        .then((res) => {
            message.success(res.message)
            getInitList()
        })
        .finally(() => {
            resetLoading.value = false
        })
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/merchant-manage/userPage", { ...pagination.value, merchantId: merInfo.value.id })
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function open(item) {
    drawerOpen.value = true
    merInfo.value = item
    dataSource.value = []
    getInitList()
}
function cancel() {
    drawerOpen.value = false
}

defineExpose({ open })
</script>

<style lang="less" scoped></style>
