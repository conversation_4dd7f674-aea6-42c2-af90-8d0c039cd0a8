<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="商户详情" @close="cancel" :footer="null">
            <div class="drawer_content">
                <div class="info_content">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="item.span" v-for="item in infoLabel" :key="item.key">
                            <div v-if="item.key === 'splitLine'" class="split_line"></div>
                            <div v-else-if="item.key === 'merchantName'">
                                <div class="title_merchant">
                                    {{ info[item.key] || "-" }}
                                    <div class="merchant_grade">{{ type[info.merchantType] }}</div>
                                </div>
                                <div class="merchant_status">{{ info.status === 0 ? "关闭" : "开启" }}</div>
                            </div>
                            <div v-else class="info">{{ item.label }}： {{ info[item.key] || "-" }}</div>
                        </a-col>
                    </a-row>
                </div>
                <div class="record_content">
                    <div class="record_title">更新记录：</div>
                    <ETable :columns="columns" :data-source="dataSource" :paginations="pagination" @change="handleTableChange">
                        <template #bodyCell="{ column, index }">
                            <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                        </template>
                    </ETable>
                </div>
            </div>
        </YDrawer>
    </div>
</template>

<script setup>
const drawerOpen = ref(false)

const info = ref({})
const type = {
    0: "服务商",
    1: "一级商户",
    2: "二级商户",
    3: "门店",
    4: "个人"
}
const infoLabel = [
    {
        key: "merchantName",
        label: "商户名称",
        span: 24
    },
    {
        key: "merchantNo",
        label: "商户ID",
        span: 8
    },
    {
        key: "areaName",
        label: "所在地区",
        span: 8
    },
    {
        key: "splitLine",
        label: "分割线",
        span: 24
    },
    {
        key: "headName",
        label: "负责人",
        span: 8
    },
    {
        key: "headPhone",
        label: "负责人电话",
        span: 8
    },
    {
        key: "headPhone",
        label: "创建人",
        span: 8
    },
    {
        key: "updateTime",
        label: "更新时间",
        span: 8
    }
]
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = ref([
    { title: "序号", dataIndex: "index" },
    { title: "用户", dataIndex: "createBy", key: "createBy" },
    { title: "更新内容", dataIndex: "changeContent", key: "changeContent" },
    { title: "更新时间", dataIndex: "updateTime", key: "updateTime" }
])

function open(data) {
    drawerOpen.value = true
    info.value = data
    getList()
}
function cancel() {
    drawerOpen.value = false
}

function getList() {
    http.post("/unicard/mgmt/merchant-manage/changeLogPage", { ...pagination.value, merchantId: info.value.id }).then((res) => {
        dataSource.value = res.data?.list
        pagination.value.pageNo = res.data?.pageNo
        pagination.value.pageSize = res.data?.pageSize
        pagination.value.total = res.data?.total
    })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
    .info_content {
        min-height: 201px;
        background: #f6f6f6;

        padding: 16px;
        border-radius: 4px;
        position: relative;
        .split_line {
            height: 1px;
            background: #e6e6e6;
            width: 100%;
        }
        .title_merchant {
            font-weight: 500;
            font-size: 18px;
            color: var(--text-color);
            line-height: 22px;
            display: flex;
            align-items: center;
            .merchant_grade {
                background: var(--primary-color-bg);
                border-radius: 4px;
                border: 1px solid var(--primary-color);
                padding: 4px;
                font-weight: 400;
                font-size: 10px;
                color: var(--primary-color);
                line-height: 12px;
                margin-left: 6px;
            }
        }
        .merchant_status {
            position: absolute;
            top: -16px;
            right: -4px;
            width: 55px;
            height: 24px;
            background: var(--primary-color);
            border-radius: 0px 4px 0px 13px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ffffff;
        }
        .info {
            color: var(--text-color);
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
        }
    }
    .record_content {
        .record_title {
            font-weight: 500;
            font-size: 16px;
            margin: 16px 0;
            color: var(--text-color);
            line-height: 22px;
        }
    }
}
</style>
