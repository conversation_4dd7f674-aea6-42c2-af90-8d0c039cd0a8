<template>
    <div>
        <YDrawer v-model:open="drawerOpen" :title="drawerTitle" @close="cancel">
            <div class="drawer_content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <!-- 一级商户没有上级商户 -->
                        <a-col :span="24" v-if="!form.pidArr?.includes('0')">
                            <a-form-item label="上级商户：" name="pidArr" mb-20 placeholder="请选择" :rules="[{ required: true, trigger: 'blur', message: '请选择上级商户' }]">
                                <a-cascader :disabled="drawerType === 'edit'" :field-names="{ label: 'name', value: 'id' }" @change="changePid" v-model:value="form.pidArr" :options="merOption" placeholder="请选择上级商户" change-on-select />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="商户类型：" name="merchantType" :rules="[{ required: true, trigger: 'blur', message: '请选择商户类型' }]">
                                <a-select :disabled="drawerType === 'edit'" ref="select" v-model:value="form.merchantType" placeholder="请选择" :options="merTypeOptions"></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="商户名称：" name="merchantName" :rules="[{ required: true, trigger: 'blur', message: '请输入商户名称' }]">
                                <a-input v-model:value.trim="form.merchantName" show-count :maxlength="50" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="所在地区：" name="areaList" :rules="[{ required: true, trigger: 'blur', message: '请选择所在地区' }]">
                                <a-cascader v-model:value="form.areaList" :field-names="{ label: 'name', value: 'name', children: 'area' }" :options="areaOptions" placeholder="请选择" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="详细地址：" name="address" :rules="[{ required: true, trigger: 'blur', message: '请输入详细地址' }]">
                                <a-input v-model:value.trim="form.address" show-count :maxlength="100" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="负责人：" name="headName" :rules="[{ required: true, trigger: 'blur', message: '请输入负责人' }]">
                                <a-input v-model:value.trim="form.headName" show-count :maxlength="50" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="负责人电话：" name="headPhone" :rules="[{ required: true, trigger: 'blur', message: '请输入负责人电话' }]">
                                <a-input v-model:value.trim="form.headPhone" show-count :maxlength="11" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YDrawer>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const emit = defineEmits(["submitDrawer"])
const drawerOpen = ref(false)
const drawerType = ref("add")
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})
const drawerTitle = computed(() => {
    const titleObj = {
        add: "新增",
        edit: "编辑",
        info: "查看"
    }
    return `${titleObj[drawerType.value]}商户`
})

const areaOptions = ref([])
const merOption = ref([])
const merTypeOptions = ref([])

const merchantTypeOption = {
    0: [{ label: "一级商户", value: 1 }],
    1: [{ label: "二级商户", value: 2 }],
    2: [
        { label: "门店", value: 3 },
        { label: "个人", value: 4 }
    ]
}

const changePid = (val, option) => {
    form.value.merchantType = null
    const obj = option && option[option.length - 1]
    if (obj) {
        if (obj.type === 3 || obj.type === 4) return
        merTypeOptions.value = merchantTypeOption[obj.type]
        form.value.pid = obj.id
    }
}

async function getMerOption() {
    await http.get("/unicard/mgmt/merchant-manage/tree").then((res) => {
        merOption.value = res.data
    })
}

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        form.value.areaName = form.value?.areaList?.join("/") || ""
        const url = drawerType.value === "edit" ? "/unicard/mgmt/merchant-manage/update" : "/unicard/mgmt/merchant-manage/create"
        http.post(url, form.value)
            .then((res) => {
                message.success(res.message)
                cancel()
                emit("submitDrawer")
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}

async function open(type, item = {}, area) {
    console.log(item, "item")
    drawerOpen.value = true
    drawerType.value = type || "add"
    areaOptions.value = area
    await getMerOption()
    if (type === "edit") {
        form.value = item || {}
        form.value.areaList = item?.areaName?.split("/") || []
        form.value.pidArr = item?.pid ? [item?.pid] : null
    } else {
        form.value = {}
    }
    merTypeOptions.value = [
        { label: "一级商户", value: 1 },
        { label: "二级商户", value: 2 },
        { label: "门店", value: 3 },
        { label: "个人", value: 4 }
    ]
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
}
</style>
