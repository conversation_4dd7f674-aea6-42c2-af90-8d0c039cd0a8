<template>
    <div class="content_box">
        <div class="form_content">
            <div class="form_box">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item
                                label="公众号APPID："
                                name="appId"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入公众号APPID'
                                    }
                                ]"
                            >
                                <a-input
                                    v-model:value.trim="form.appId"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="公众号Appsecret："
                                name="appSecret"
                            >
                                <a-input
                                    v-model:value.trim="form.appSecret"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="商户名称："
                                name="mchName"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入商户名称'
                                    }
                                ]"
                            >
                                <a-input
                                    v-model:value.trim="form.mchName"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="商户mchid："
                                name="mchId"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入商户mchid'
                                    }
                                ]"
                            >
                                <a-input
                                    v-model:value.trim="form.mchId"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="商户API证书序列号："
                                name="apiSerialNo"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入商户API证书序列号'
                                    }
                                ]"
                            >
                                <a-input
                                    v-model:value.trim="form.apiSerialNo"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="商户API v3密钥："
                                name="apiV3Secret"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入商户API v3密钥'
                                    }
                                ]"
                            >
                                <a-input
                                    v-model:value.trim="form.apiV3Secret"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="商户支付证书私钥："
                                name="fileList"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请上传商户支付证书私钥'
                                    }
                                ]"
                            >
                                <div
                                    v-if="form.fileList?.length"
                                    class="preview_file"
                                >
                                    <span> {{ form.fileList[0]?.name }}</span>
                                </div>
                                <a-upload
                                    v-else
                                    ref="uploads"
                                    accept=".pem"
                                    action="/"
                                    :multiple="false"
                                    :show-upload-list="false"
                                    :file-list="form.fileList"
                                    :limit="1"
                                    :before-upload="
                                        (file) => beforeUpload(file)
                                    "
                                >
                                    <a-button
                                        type="primary"
                                        ghost
                                        style="
                                            background: var(--primary-color-bg);
                                        "
                                        >上传附件</a-button
                                    >
                                </a-upload>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="p12文件：" name="p12FileList">
                                <div
                                    v-if="form.p12FileList"
                                    class="preview_file"
                                >
                                    <span>
                                        {{ form.p12FileList[0]?.name }}</span
                                    >
                                </div>
                                <a-upload
                                    ref="uploads"
                                    accept=".p12"
                                    v-else
                                    action="/"
                                    :multiple="false"
                                    :show-upload-list="false"
                                    :file-list="form.p12FileList"
                                    :limit="1"
                                    :before-upload="
                                        (file) => certP12BeforeUpload(file)
                                    "
                                >
                                    <a-button
                                        type="primary"
                                        ghost
                                        style="
                                            background: var(--primary-color-bg);
                                        "
                                        >上传附件</a-button
                                    >
                                </a-upload>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="商户支付类型："
                                name="payTypesList"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择商户支付类型'
                                    }
                                ]"
                            >
                                <a-checkbox-group
                                    v-model:value="form.payTypesList"
                                    :options="[
                                        {
                                            label: 'H5支付',
                                            value: '4'
                                        },
                                        {
                                            label: '付款码支付',
                                            value: '7'
                                        }
                                    ]"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </div>
        <div class="footer">
            <a-button @click="cancel">取消</a-button>
            <a-button type="primary" :loading="submitLoading" @click="submit"
                >确定</a-button
            >
        </div>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
import { watch } from "vue"

const props = defineProps({
    info: {
        type: Object,
        default: () => {
            return {}
        }
    }
})
const emit = defineEmits(["cancelDrawer"])
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})

// WX_JSAPI(1, "公众号支付"),  //公众号支付
// WX_APP(2, "APP支付"),  //H5支付
// WX_NATIVE(3, "Native支付"),  //H5支付
// WX_H5(4, "H5支付"),  //H5支付
// WX_APPLET(5, "小程序支付"),  //小程序支付
// WX_FACE(6, "刷脸支付"),  //刷脸支付
// WX_MICRO(7, "付款码支付"),  //付款码支付

watch(
    () => props.info,
    (newVal) => {
        console.log(newVal)
        form.value = newVal || {}
        form.value.payTypesList = newVal?.payTypes?.split(",") || []
        form.value.fileList = newVal.privateKeyName
            ? [{ name: newVal.privateKeyName }]
            : []
        form.value.p12FileList = newVal.certP12Name
            ? [{ name: newVal.certP12Name }]
            : []
        console.log(form.value.payTypesList)
    }
)

// 判断图片大小以及转成base64
const beforeUpload = (file) => {
    const imageSize = file.size / 1024 / 1024
    if (imageSize > 1) {
        message.error(`图片大小为${imageSize.toFixed(2)}M, 大于${1}M`)
        return false
    }
    form.value.file = file
    form.value.fileList = [
        { name: file.name, uid: file.uid, size: file.size, type: file.type }
    ]
    return false
}

// 判断图片大小以及转成base64
const certP12BeforeUpload = (file) => {
    const imageSize = file.size / 1024 / 1024
    if (imageSize > 1) {
        message.error(`图片大小为${imageSize.toFixed(2)}M, 大于${1}M`)
        return false
    }
    console.log(file)

    form.value.p12File = file
    form.value.p12FileList = [
        { name: file.name, uid: file.uid, size: file.size, type: file.type }
    ]
    return false
}
function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        form.value.payTypes = form.value.payTypesList.join(",")
        http.postForm("/unicard/mgmt/wx-pay-config/update", form.value)
            .then((res) => {
                message.success(res.message)
                cancel()
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}

function cancel() {
    emit("cancelDrawer")
}
</script>

<style lang="less" scoped>
.content_box {
    position: relative;
    max-height: calc(100vh - 268px);
    height: calc(100vh - 268px);
    overflow: hidden;
    width: 100%;
    padding-bottom: 100px;

    .form_content {
        width: 100%;
        height: 100%;
        max-height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        .form_box {
            max-width: 1200px;
            margin: auto;
        }
    }
    .image_preview_mask {
        display: flex;
    }
    .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-top: 1px solid #d8d8d8;
        padding: 20px 0;
        background: var(--bg-color);
    }
    .preview_file {
        height: 50px;
        width: 200px;
        border-radius: 10px;
        padding: 0px 10px;
        border: 1px solid #d8d8d8;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
</style>
