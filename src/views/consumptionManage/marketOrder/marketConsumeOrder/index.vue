<template>
    <div class="container">
        <a-space class="reset-space" :size="12">
            <a-card
                class="card-item"
                hoverable
                v-for="(item, index) in cardMetas"
                :key="index"
            >
                <a-card-meta
                    :title="`${state.cardMetasData[item.value] || 0} ${
                        item.unit
                    }`"
                >
                    <template #description>{{ item.description }}</template>
                </a-card-meta>
            </a-card>
        </a-space>

        <searchForm
            class="reset-search-form"
            v-model:formState="state.form"
            layout="horizontal"
            :formList="formList"
            @submit="queryInitData"
            @reset="queryInitData"
        ></searchForm>

        <ETable
            :columns="columns"
            :minH="450"
            :data-source="state.dataSource"
            :paginations="state.pagination"
            :scroll="{ x: 1600 }"
            :loading="state.loading"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, text, record, index }">
                <template v-if="column.dataIndex === 'index'">{{
                    index + 1
                }}</template>
                <template v-else-if="column.dataIndex === 'businessJson'">
                    {{ JSON.parse(record.businessJson)?.planName }}
                </template>
                <template v-else-if="column.dataIndex === 'operate'">
                    <a-button
                        :disabled="![2, 5].includes(record.orderStatus)"
                        type="link"
                        class="btn-link-color"
                        @click="handlerRefundOpen(record)"
                    >
                        退款
                    </a-button>
                </template>
                <Tooltip
                    v-else
                    :maxWidth="column.width - 10"
                    :title="text"
                ></Tooltip>
            </template>
        </ETable>

        <YModal
            v-model:open="state.refundOpen"
            title="退款申请"
            width="700px"
            :bodyStyle="{ padding: '24px' }"
            @cancel="handleRefundCancel"
        >
            <ul class="refund-list">
                <li
                    class="refund-item"
                    v-for="item in refundApplication"
                    :key="item.key"
                >
                    <span class="refund-item-title">{{ item.title }}</span>
                    <span class="refund-item-money">{{
                        state.refundForm[item.key] || "-"
                    }}</span>
                </li>
            </ul>

            <a-form
                class="refund-form"
                :model="state.refundForm"
                ref="formRef"
                layout="vertical"
            >
                <a-form-item
                    style="margin-bottom: 16px"
                    name="refundAmount"
                    label="本次可退款金额"
                    :rules="[
                        {
                            required: true,
                            trigger: 'blur',
                            message: '请输入本次可退款金额'
                        }
                    ]"
                >
                    <a-input-number
                        v-model:value.trim="state.refundForm.refundAmount"
                        placeholder="请输入本次可退款金额"
                        :step="1"
                        :precision="2"
                        style="width: 100%"
                    />
                </a-form-item>
                <a-form-item name="refundReason">
                    <template #label>
                        <div class="refund-title">
                            <a-divider type="vertical" class="refund-divider" />
                            退款原因：
                        </div>
                    </template>
                    <a-textarea
                        v-model:value.trim="state.refundForm.refundReason"
                        placeholder="请输入退款原因"
                        show-count
                        :maxlength="100"
                        :auto-size="{ minRows: 6, maxRows: 5 }"
                    />
                </a-form-item>
            </a-form>
            <template #footer>
                <a-button key="back" @click="handleRefundCancel">取消</a-button>
                <a-button
                    key="submit"
                    type="primary"
                    :loading="state.refundLoading"
                    @click="handleRefundOk"
                    >确定</a-button
                >
            </template>
        </YModal>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const state = reactive({
    cardMetasData: {
        todayOrderNum: 0,
        todayPayAmount: 0,
        totalOrderNum: 0,
        totalPayAmount: 0
    },
    form: {},
    dataSource: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    sort: {
        field: "",
        order: ""
    },
    // 退款申请
    refundForm: {},
    refundOpen: false,
    refundLoading: false,
    businessType: 3 // 1.充值 2.食堂消费 3.超市消费
})

const payMethodList = ref([])
const formList = computed(() => {
    return [
        {
            type: "input",
            value: "orderNo",
            label: "内部订单号"
        },
        {
            type: "input",
            value: "transactionId",
            label: "交易流水号"
        },
        {
            type: "input",
            value: "orderUserName",
            label: "姓名"
        },
        {
            type: "select",
            value: "payMethod",
            label: "支付渠道",
            list: payMethodList.value,
            attrs: {
                fieldNames: {
                    label: "payMethodName",
                    value: "payMethodCode"
                }
            }
        },
        {
            type: "select",
            value: "orderStatus",
            label: "订单状态",
            list: [
                { label: "全部", value: null },
                { label: "未支付", value: 1 },
                { label: "已支付", value: 2 },
                { label: "已关闭", value: 3 },
                { label: "已支付（部分退款）", value: 4 }
            ]
        },
        {
            type: "rangePicker",
            label: "创建订单时间",
            value: ["crateTime", "endTime"],
            voidValue: "endTime",
            attrs: {
                valueFormat: "YYYY-MM-DD",
                format: "YYYY-MM-DD",
                showTime: true
            }
        },
        {
            type: "rangePicker",
            label: "支付时间",
            value: ["startPayTime", "endPayTime"],
            voidValue: "endPayTime",
            attrs: {
                valueFormat: "YYYY-MM-DD",
                format: "YYYY-MM-DD",
                showTime: true
            }
        }
    ]
})

const columns = [
    { title: "序号", dataIndex: "index", key: "index", width: 80 },
    {
        title: "收款商户",
        dataIndex: "merchantName",
        key: "merchantName",
        width: 100
    },
    { title: "内部订单号", dataIndex: "orderNo", width: 160 },
    { title: "外部订单号", dataIndex: "tradeNo", width: 160 },
    { title: "交易流水号", dataIndex: "transactionId", width: 160 },
    { title: "姓名", dataIndex: "orderUserName" },
    { title: "金额", dataIndex: "payAmount", width: 80 },
    {
        title: "创建订单时间",
        dataIndex: "createTime",
        sorter: true,
        width: 180
    },
    { title: "订单状态", dataIndex: "orderStatusName" },
    { title: "支付渠道", dataIndex: "payMethod" },
    { title: "支付时间", dataIndex: "payTime", sorter: true, width: 180 },
    { title: "操作人", dataIndex: "updateBy" },
    { title: "操作", dataIndex: "operate", fixed: "right", width: 80 }
]

const refundApplication = [
    { title: "项目：", key: "title" },
    { title: "姓名：", key: "payName" },
    { title: "学号：", key: "orderUserName" },
    { title: "卡号：", key: "payPhone" },
    { title: "充值金额：", key: "payAmount" },
    { title: "已退款金额：", key: "totalRefundAmount" }
]

// 今日订单
const cardMetas = [
    {
        unit: "笔",
        description: "今日订单",
        value: "todayOrderNum"
    },
    {
        unit: "元",
        description: "今日收款总额",
        value: "todayPayAmount"
    },
    {
        unit: "笔",
        description: "订单总数",
        value: "totalOrderNum"
    },
    {
        unit: "元",
        description: "订单总金额",
        value: "totalPayAmount"
    }
]

// 退款申请确定
const handleRefundOk = () => {
    state.refundLoading = true
    http.post(
        "/campuspay/general-mgmt/pay-order/refund-request",
        state.refundForm
    )
        .then(({ msg }) => {
            message.success(msg)
            handleRefundCancel()
            getInitData()
        })
        .finally(() => {
            state.refundLoading = false
        })
}

// 退款申请取消
const handleRefundCancel = () => {
    state.refundOpen = false
    state.refundForm.refundReason = ""
}

// 退款申请弹框
const handlerRefundOpen = (item) => {
    state.refundForm = item
    state.refundForm.refundReason = ""
    state.refundOpen = true
}

// 常规支付-交易订单-统计
const getOrderStatistics = () => {
    http.post("/unicard/mgmt/pay-order/order-statistics", {
        businessType: state.businessType
    }).then(({ data }) => {
        state.cardMetasData = data
    })
}

// 常规支付-交易订单-分页
const getInitData = () => {
    state.loading = true
    const params = {
        ...state.form,
        ...state.pagination,
        businessType: state.businessType
    }
    if (state.sort.field) {
        params.field = state.sort.field
        params.order = state.sort.order
    }
    http.post("/unicard/mgmt/pay-order/payment-order-page", params)
        .then((res) => {
            const { pageNo, pageSize, total, list } = res.data
            state.dataSource = list
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
        .finally(() => {
            state.loading = false
        })
    state.dataSource = [{ name: 1, orderStatus: 2 }]
}

// 分页
const handleTableChange = (
    { current, pageSize, total },
    filters,
    { field, order }
) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    state.sort.field = ""
    state.sort.order = ""
    if (order) {
        state.sort.field = field === "createTime" ? "create_time" : "pay_time"
        state.sort.order = order === "ascend" ? "asc" : "desc"
    }
    getInitData()
}

// 查询 重置
const queryInitData = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getInitData()
}

function getPayMethod() {
    http.get("/unicard/mgmt/pay-order/pay-method-list").then((res) => {
        payMethodList.value = res.data
    })
}

onMounted(() => {
    getPayMethod()
    getOrderStatistics()
    getInitData()
})
</script>

<style lang="less" scoped>
.container {
    padding: 20px;
    .reset-space {
        width: 100%;

        .card-item {
            min-width: 140px;
            text-align: center;
        }
    }

    .reset-search-form {
        margin: 24px 0;
    }
    :deep(.ant-table-column-has-sorters) {
        .ant-table-column-sorters {
            justify-content: flex-start;

            .ant-table-column-title {
                flex: none;
            }
        }
    }
}
.refund-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    // 每行3列
    .refund-item {
        width: 33%;
        margin-bottom: 24px;
        display: flex;

        .refund-item-title {
            font-size: 14px;
            font-weight: 400;
            color: #000000a6;
        }

        .refund-item-money {
            // 字数过多省略
            width: max-content;
            display: inline-block;
            width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: bottom;
            flex: 1;
        }
    }
}

.refund-title {
    color: #000000a6;

    .refund-divider {
        height: 16px;
        width: 3px;
        margin-left: 0;
        background-color: var(--primary-color);
    }
}
</style>
