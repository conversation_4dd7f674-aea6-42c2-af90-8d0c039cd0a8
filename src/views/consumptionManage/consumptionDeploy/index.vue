<template>
    <div class="consumption_deploy">
        <div class="header">消费配置</div>
        <div class="content">
            <div class="btn_group">
                <a-button type="primary" @click="addSceneFn('add')">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建场景
                </a-button>
                <a-button @click="userLimitFn">人员消费限制</a-button>
                <a-button @click="deviceLimitFn">设备消费限制</a-button>
            </div>
            <ETable
                :columns="columns"
                :data-source="dataSource"
                :paginations="pagination"
                @change="handleTableChange"
                :loading="tableLoading"
            >
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.dataIndex == 'index'">{{
                        index + 1
                    }}</template>
                    <template v-else-if="column.dataIndex == 'operate'">
                        <a-button
                            type="link"
                            class="btn-link-color"
                            @click="addSceneFn('edit', record)"
                            >编辑</a-button
                        >
                        <a-button
                            type="link"
                            danger
                            ghost
                            @click="delectScene(record)"
                            >删除</a-button
                        >
                    </template>
                </template>
            </ETable>
        </div>
        <!-- 新增编辑场景 -->
        <add-scene ref="addSceneRef" @submitDrawer="getInitList" />

        <!-- 人员限制 -->
        <user-limit ref="userLimitRef" />

        <!-- 设备限制 -->
        <device-limit ref="deviceLimitRef" />
    </div>
</template>

<script setup>
import { message, Modal } from "ant-design-vue"
import { createVNode } from "vue"
import { ExclamationCircleFilled } from "@ant-design/icons-vue"
import AddScene from "./components/addScene.vue"
import DeviceLimit from "./components/deviceLimit.vue"
import UserLimit from "./components/userLimit.vue"

const dataSource = ref([])
const tableLoading = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = ref([
    { title: "序号", dataIndex: "index" },
    { title: "场景名称", dataIndex: "typeName", key: "typeName" },
    { title: "更新时间", dataIndex: "updateTime", key: "updateTime" },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

const addSceneRef = ref(null)
const userLimitRef = ref(null)
const deviceLimitRef = ref(null)

// 新增编辑场景
function addSceneFn(type, item = {}) {
    addSceneRef.value.open(type, {
        id: item?.id || null,
        typeName: item?.typeName || ""
    })
}

// 人员限制
function userLimitFn() {
    userLimitRef.value.open()
}

// 设备限制
function deviceLimitFn() {
    deviceLimitRef.value.open()
}

// 删除场景
function delectScene(item) {
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "确定删除当前类型吗？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            http.post("/unicard/mgmt/consumption-type/delete", {
                id: item.id
            }).then((res) => {
                message.success(res.message)
                getList()
            })
        },
        onCancel() {
            message.info("已取消！")
        }
    })
}

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/consumption-type/page", pagination.value)
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

onMounted(() => {
    getInitList()
})
</script>

<style lang="less" scoped>
.consumption_deploy {
    .header {
        padding: 18px 20px;
        font-weight: 500;
        font-size: 18px;
        color: #262626;
        line-height: 25px;
        border-bottom: 1px solid #d9d9d9;
    }
    .content {
        padding: 20px;

        .btn_group {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 16px;
        }
    }
}
</style>
