<template>
    <div>
        <YModal v-model:open="openModal" title="人员消费限制" @close="cancel" @cancel="cancel" @confirm="submit" :width="860">
            <div class="content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item label="状态：" name="status" :rules="[{ required: true, trigger: 'blur', message: '请选择状态' }]">
                                <a-radio-group v-model:value="form.status" name="radioGroup">
                                    <a-radio :value="0">开启</a-radio>
                                    <a-radio :value="1">关闭</a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="日限次：" name="dayNum" :rules="[{ required: true, trigger: 'blur', message: '请输入日限次' }]">
                                <a-input-number style="width: 100%" :step="1" :min="0" :max="100000000" :precision="2" v-model:value.trim="form.dayNum" placeholder="请输入"> </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="月限次：" name="monthNum" :rules="[{ required: true, trigger: 'blur', message: '请输入月限次' }]">
                                <a-input-number style="width: 100%" :step="1" :min="0" :max="100000000" :precision="2" v-model:value.trim="form.monthNum" placeholder="请输入"> </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="日限额：" name="dayAmount" :rules="[{ required: true, trigger: 'blur', message: '请输入日限额' }]">
                                <a-input-number style="width: 100%" :step="1" :min="0" :max="100000000" :precision="2" v-model:value.trim="form.dayAmount" placeholder="请输入"> </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="月限额：" name="monthAmount" :rules="[{ required: true, trigger: 'blur', message: '请输入月限额' }]">
                                <a-input-number style="width: 100%" :step="1" :min="0" :max="9999999999.99" :precision="2" v-model:value.trim="form.monthAmount" placeholder="请输入"> </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="备注说明：" name="remarks">
                                <a-textarea v-model:value.trim="form.remarks" placeholder="请输入" :auto-size="{ minRows: 10, maxRows: 10 }" show-count :maxlength="50" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YModal>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const emit = defineEmits(["submitDrawer"])
const openModal = ref(false)
const submitLoading = ref(false)

const formRef = ref(null)
const form = ref({})

function getPersonInfo() {
    http.post("/unicard/mgmt/consumption-person/detail").then((res) => {
        form.value = res.data || {}
    })
}

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        http.post("/unicard/mgmt/consumption-person/edit", form.value)
            .then((res) => {
                message.success(res.message)
                cancel()
                emit("submitDrawer")
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}

function open() {
    openModal.value = true
    getPersonInfo()
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
}
</style>
