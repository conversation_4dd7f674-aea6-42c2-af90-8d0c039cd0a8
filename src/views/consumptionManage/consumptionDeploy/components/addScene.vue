<template>
    <div>
        <YModal v-model:open="openModal" :title="modalTitle" @close="cancel" @cancel="cancel" @confirm="submit">
            <div class="content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-form-item label="场景名称：" name="typeName" :rules="[{ required: true, trigger: 'blur', message: '请输入场景名称' }]">
                        <a-input style="width: 100%" v-model:value.trim="form.typeName" placeholder="请输入" show-count :maxlength="8" />
                    </a-form-item>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YModal>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"

const emit = defineEmits(["submitDrawer"])
const openModal = ref(false)
const submitLoading = ref(false)
const modalType = ref("add")

const formRef = ref(null)
const form = ref({})

const modalTitle = computed(() => {
    return modalType.value === "add" ? "新建场景" : "编辑场景"
})

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        const url = modalType.value === "edit" ? "/unicard/mgmt/consumption-type/update" : "/unicard/mgmt/consumption-type/create"
        http.post(url, form.value)
            .then((res) => {
                message.success(res.message)
                cancel()
                emit("submitDrawer")
            })
            .finally(() => {
                submitLoading.value = false
            })
        emit("submitDrawer")
    })
}

function open(type, item) {
    modalType.value = type
    openModal.value = true
    form.value = item
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
}
</style>
