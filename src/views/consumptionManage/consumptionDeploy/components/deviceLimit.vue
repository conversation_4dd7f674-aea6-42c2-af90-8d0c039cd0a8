<template>
    <div>
        <YModal
            v-model:open="openModal"
            title="设备消费限制"
            @close="cancel"
            @cancel="cancel"
            @confirm="submit"
            :width="860"
        >
            <div class="content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item
                                label="状态："
                                name="status"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择状态'
                                    }
                                ]"
                            >
                                <a-radio-group
                                    v-model:value="form.status"
                                    name="radioGroup"
                                >
                                    <a-radio :value="1">开启</a-radio>
                                    <a-radio :value="0">关闭</a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="选择设备："
                                name="deviceIds"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择设备'
                                    }
                                ]"
                            >
                                <a-select
                                    mode="multiple"
                                    ref="select"
                                    v-model:value="form.deviceIds"
                                    placeholder="请选择"
                                    :options="typeOptions"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="离线消费："
                                name="offlineDay"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入天数'
                                    }
                                ]"
                            >
                                <a-input-number
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="form.offlineDay"
                                    placeholder="请输入"
                                    addon-after="天"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <div class="time_item">
                                <a-form-item
                                    name="offlineMinute"
                                    label=" "
                                    :rules="[
                                        {
                                            required: true,
                                            trigger: 'blur',
                                            message: '请输入分钟'
                                        }
                                    ]"
                                >
                                    <a-input-number
                                        style="width: 100%"
                                        :step="1"
                                        :precision="2"
                                        v-model:value.trim="form.offlineMinute"
                                        placeholder="请输入"
                                        addon-after="分钟"
                                    >
                                    </a-input-number>
                                </a-form-item>
                            </div>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="备注说明：" name="remarks">
                                <a-textarea
                                    v-model:value.trim="form.remarks"
                                    placeholder="请输入"
                                    :auto-size="{ minRows: 10, maxRows: 10 }"
                                    show-count
                                    :maxlength="500"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button
                    type="primary"
                    :loading="submitLoading"
                    @click="submit"
                    >确定</a-button
                >
            </template>
        </YModal>
    </div>
</template>

<script setup>
const emit = defineEmits(["submitDrawer"])
const openModal = ref(false)
const submitLoading = ref(false)

const formRef = ref(null)
const form = ref({})

const typeOptions = ref([
    {
        label: "人脸",
        value: "miniprogram"
    },
    {
        label: "卡号",
        value: "wxpay"
    }
])

function getDeviceInfo() {
    http.post("/unicard/mgmt/consumption-device/detail").then((res) => {
        form.value = res.data || {}
        form.value.deviceIds =
            res.data?.deviceRelatedList.map((i) => i.deviceId) || []
    })
}

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
        // http.post(url, form.value)
        //     .then((res) => {
        //         YMessage.success(res.message)
        //         cancel()
        //         emit("submitDrawer")
        //     })
        //     .finally(() => {
        //         submitLoading.value = false
        //     })
        emit("submitDrawer")
    })
}

function open() {
    openModal.value = true
    getDeviceInfo()
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
    .time_item {
        :deep(.ant-form-item-required::before) {
            content: "" !important;
        }
    }
}
</style>
