<template>
    <div class="people_tree">
        <a-spin :spinning="spinning" tip="加载中...">
            <DempTree
                class="demp_content"
                :treeData="treeData"
                :defaultExpand="defaultExpand"
                :selectedKeys="selectedKeys"
                :fieldNames="fieldNames"
                @emitSelect="handrSelect"
            >
                <template #handleFooter>
                    <a-radio-group
                        v-model:value="peopleType"
                        button-style="solid"
                        @change="getTreeData"
                    >
                        <a-radio-button value="teacher">老师</a-radio-button>
                        <a-radio-button value="student">学生</a-radio-button>
                    </a-radio-group>

                    <!--  -->
                    <a-input
                        style="margin: 20px 0"
                        v-model:value="searchData"
                        placeholder="搜索部门/姓名/手机号"
                    >
                        <template #suffix>
                            <SearchOutlined />
                        </template>
                    </a-input>
                </template>
            </DempTree>
        </a-spin>
    </div>
</template>

<script setup>
const emit = defineEmits(["changeKey"])
const spinning = ref(false)
const peopleType = ref("teacher")
const searchData = ref("")
const selectedKeys = ref([])
const defaultExpand = ref([])
const fieldNames = { children: "children", title: "showName", key: "id" }

const treeData = ref([])

const getTeacherTree = async () => {
    spinning.value = true
    await http
        .get("/cloud/app/dept/list")
        .then((res) => {
            treeData.value = res.data
            if (res.data?.length) {
                handrSelect(res.data[0]?.id, res.data[0])
            }
        })
        .finally(() => {
            spinning.value = false
        })
}

const getStudentTree = async () => {
    spinning.value = true
    await http
        .get("/cloud/app/roll/listTree")
        .then((res) => {
            treeData.value = res.data
            if (res.data?.length) {
                handrSelect(res.data[0]?.id, res.data[0])
            }
        })
        .finally(() => {
            spinning.value = false
        })
}

const getTreeData = async () => {
    treeData.value = []
    selectedKeys.value = []
    defaultExpand.value = []
    if (peopleType.value === "teacher") {
        await getTeacherTree()
    } else {
        await getStudentTree()
    }
}

const handrSelect = (ids, item) => {
    selectedKeys.value = [ids]
    emit("changeKey", ids, item)
}

defineExpose({ peopleType, getTreeData })
</script>

<style lang="less" scoped>
.people_tree {
    padding: 20px 0px;
    :deep(.ant-radio-group) {
        display: flex;
        width: 100%;
    }
    :deep(.ant-radio-button-wrapper) {
        flex: 1;
        text-align: center;
    }
    .demp_content {
        padding: 0 16px;
        overflow: hidden auto;
        height: calc(100vh - 168px);
    }
}
</style>
