<template>
    <div>
        <YModal
            v-model:open="openModal"
            :title="modalType === 'add' ? '注销' : '详情'"
            @cancel="cancel"
            :width="1024"
            @confirm="submitModal"
            :footer="modalType === 'add' ? undefined : null"
        >
            <div class="report_loss_content">
                <a-form
                    :model="form"
                    ref="formRef"
                    :layout="modalType === 'add' ? 'vertical' : 'horizontal'"
                >
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24" v-if="modalType === 'add'">
                            <a-form-item
                                label="选择人员："
                                name="personName"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择人员'
                                    }
                                ]"
                            >
                                <a-input
                                    v-model:value.trim="form.personName"
                                    readOnly
                                    @click="() => (selectOpen = true)"
                                    placeholder="请选择"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-table
                                :columns="personnelColumns"
                                :scroll="{ x: 500 }"
                                :data-source="personnelDataSource"
                                :pagination="false"
                            >
                                <template #bodyCell="{ column, record }">
                                    <template
                                        v-if="column.dataIndex === 'gender'"
                                    >
                                        {{
                                            ["女", "男"][record.gender] ||
                                            "未知"
                                        }}
                                    </template>
                                </template>
                                <template #emptyText>
                                    <Empty
                                        :emptyStyle="{
                                            marginBottom: '65px'
                                        }"
                                    ></Empty>
                                </template>
                            </a-table>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="注销原因："
                                name="remark"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入注销原因'
                                    }
                                ]"
                            >
                                <a-textarea
                                    v-if="modalType === 'add'"
                                    v-model:value.trim="form.remark"
                                    placeholder="请输入"
                                    :auto-size="{ minRows: 10, maxRows: 10 }"
                                />
                                <span v-else>{{ form.remark }}</span>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="退款金额："
                                name="refundAmount"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入退款金额'
                                    }
                                ]"
                            >
                                <a-input-number
                                    :disabled="modalType === 'info'"
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="form.refundAmount"
                                    placeholder="请输入"
                                    addon-after="元"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="余额退回方式："
                                name="payMethod"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择余额退回方式'
                                    }
                                ]"
                            >
                                <a-select
                                    ref="select"
                                    :disabled="modalType === 'info'"
                                    v-model:value="form.payMethod"
                                    placeholder="请选择"
                                    :options="[{ label: '现金', value: 10 }]"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12" v-if="modalType === 'info'">
                            <a-form-item label="操作人：" name="createBy">
                                {{ form.createBy }}
                            </a-form-item>
                        </a-col>
                        <a-col :span="12" v-if="modalType === 'info'">
                            <a-form-item label="操作时间：" name="createTime">
                                {{ form.createTime }}
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </YModal>
        <YSelect
            v-model:visible="selectOpen"
            :tabs="selectTabs"
            :maximum="100"
            :isPickType="['person']"
            @confirm="handerConfirm"
        />
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const emit = defineEmits(["submitDrawer"])
const openModal = ref(false)
const submitLoading = ref(false)
const modalType = ref("add")
const personnelDataSource = ref([])
const formRef = ref(null)
const form = ref({})
const personnelColumns = ref([
    { title: "工号", dataIndex: "jobNumber", key: "jobNumber", width: 100 },
    { title: "姓名", dataIndex: "name", key: "name", width: 100 },
    {
        title: "性别",
        dataIndex: "gender",
        key: "gender",
        width: 100
    },
    { title: "所在部门", dataIndex: "deptName", key: "deptName", width: 100 },
    { title: "卡号", dataIndex: "cardNo", key: "cardNo", width: 100 },
    {
        title: "卡内余额",
        dataIndex: "balance",
        key: "balance",
        width: 100
    },
    {
        title: "卡内押金",
        dataIndex: "cashPledge",
        key: "cashPledge",
        width: 100
    }
])

const selectOpen = ref(false)
const selectTabs = [
    {
        tab: "教职工",
        key: 2,
        businessType: 20,
        code: null,
        single: true, // 单选多选
        checked: true
    },
    {
        tab: "学生",
        key: 1,
        businessType: 10,
        code: null,
        single: true, // 单选多选
        checked: true
    }
]

const handerConfirm = (data) => {
    personnelDataSource.value = data || []
    form.value.personId = data[0]?.id
    form.value.personName = data?.map((i) => i.name)?.join(",")
    selectOpen.value = false
}

// 弹框确认
function submitModal() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        http.post("/unicard/mgmt/card-op-log/cancelled", form.value)
            .then((res) => {
                message.success(res.message)
                cancel()
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}

function getInfo(item) {
    http.post("/unicard/mgmt/card-op-log/get-cancelled", { id: item.id }).then(
        (res) => {
            form.value = res.data
            personnelDataSource.value = [
                {
                    ...res.data,
                    name: res.data?.personName,
                    id: res.data?.personId
                }
            ]
        }
    )
}

function open(type, item = {}) {
    modalType.value = type
    openModal.value = true
    if (type === "info") {
        getInfo(item)
    } else {
        form.value = {}
        personnelDataSource.value = []
    }
}
function cancel() {
    openModal.value = false
    emit("submitDrawer")
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.report_loss_content {
    padding: 20px;
}
</style>
