<template>
    <div>
        <YModal
            v-model:open="openModal"
            title="补办/换卡"
            @cancel="cancel"
            :width="1024"
            @confirm="submitModal"
        >
            <div class="report_loss_content">
                <a-form
                    :model="form"
                    ref="formRef"
                    :layout="modalType === 'add' ? 'vertical' : 'horizontal'"
                >
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24" v-if="modalType === 'add'">
                            <a-form-item
                                label="选择人员："
                                name="personName"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择人员'
                                    }
                                ]"
                            >
                                <a-input
                                    v-model:value.trim="form.personName"
                                    readOnly
                                    @click="() => (selectOpen = true)"
                                    placeholder="请选择"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                :label="modalType === 'add' ? '挂失记录：' : ''"
                            >
                                <a-table
                                    :columns="personnelColumns"
                                    :scroll="{ x: 500 }"
                                    :data-source="personnelDataSource"
                                    :pagination="false"
                                >
                                    <template #bodyCell="{ column, record }">
                                        <template
                                            v-if="column.dataIndex === 'gender'"
                                        >
                                            {{
                                                ["女", "男"][record.gender] ||
                                                "未知"
                                            }}
                                        </template>
                                    </template>
                                    <template #emptyText>
                                        <Empty
                                            :emptyStyle="{
                                                marginBottom: '65px'
                                            }"
                                        ></Empty>
                                    </template>
                                </a-table>
                            </a-form-item>
                        </a-col>
                        <a-col :span="8">
                            {{ "卡片补办(原卡号)：" }}
                            {{ form.cardNo || "-" }}
                        </a-col>
                        <a-col :span="8">
                            {{ "原卡余额：" }} {{ form.balance || "-" }}
                        </a-col>
                        <a-col :span="8">
                            {{ "消费类型：" }} {{ form.majorName || "-" }}
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="补办卡片类型："
                                name="reissueCardType"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择补办卡片类型'
                                    }
                                ]"
                            >
                                <a-select
                                    ref="select"
                                    v-model:value="form.reissueCardType"
                                    placeholder="请选择"
                                    :options="[
                                        {
                                            label: 'IC电子卡',
                                            value: 'icElectronicCard'
                                        }
                                    ]"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="选择卡号："
                                name="reissueCardId"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择卡号'
                                    }
                                ]"
                            >
                                <a-select
                                    ref="select"
                                    :disabled="modalType === 'info'"
                                    v-model:value="form.reissueCardId"
                                    placeholder="请选择"
                                    :options="cardNumList"
                                    :fieldNames="{
                                        label: 'cardNo',
                                        value: 'id'
                                    }"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="补办费用："
                                name="reissueAmount"
                            >
                                <a-input-number
                                    :disabled="modalType === 'info'"
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="form.reissueAmount"
                                    placeholder="请输入"
                                    addon-after="元"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="消费类型："
                                name="typeIds"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择消费类型'
                                    }
                                ]"
                            >
                                <a-select
                                    ref="select"
                                    v-model:value="form.typeIds"
                                    placeholder="请选择"
                                    :disabled="modalType === 'info'"
                                    :options="typeList"
                                    :fieldNames="{
                                        label: 'typeName',
                                        value: 'id'
                                    }"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="卡有效期：">
                                <RangePicker
                                    :disabled="modalType === 'info'"
                                    v-model:startTime="form.validityStartDate"
                                    v-model:endTime="form.validityEndDate"
                                ></RangePicker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="卡片押金："
                                name="reissueCashPledge"
                            >
                                <a-input-number
                                    :disabled="modalType === 'info'"
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="form.reissueCashPledge"
                                    placeholder="请输入"
                                    addon-after="元"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="资金来源："
                                name="payMethod"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择资金来源'
                                    }
                                ]"
                            >
                                <a-select
                                    :disabled="modalType === 'info'"
                                    ref="select"
                                    v-model:value="form.payMethod"
                                    placeholder="请选择"
                                    :options="[{ label: '现金', value: 10 }]"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="补办说明：" name="remark">
                                <a-textarea
                                    :maxlength="500"
                                    :disabled="modalType === 'info'"
                                    v-model:value.trim="form.remark"
                                    placeholder="请输入"
                                    :auto-size="{ minRows: 10, maxRows: 10 }"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12" v-if="modalType === 'info'">
                            <a-form-item label="操作人：" name="createBy">
                                {{ form.createBy }}
                            </a-form-item>
                        </a-col>
                        <a-col :span="12" v-if="modalType === 'info'">
                            <a-form-item label="操作时间：" name="createTime">
                                {{ form.createTime }}</a-form-item
                            >
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </YModal>
        <YSelect
            v-model:visible="selectOpen"
            :tabs="selectTabs"
            :maximum="100"
            :isPickType="['person']"
            @confirm="handerConfirm"
        />
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const emit = defineEmits(["submitDrawer"])
const openModal = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})
const personnelDataSource = ref([])
const modalType = ref("add")
const cardNumList = ref([])
const typeList = ref([])

const personnelColumns = ref([
    { title: "工号", dataIndex: "jobNumber", key: "jobNumber", width: 100 },
    { title: "姓名", dataIndex: "name", key: "name", width: 100 },
    {
        title: "性别",
        dataIndex: "gender",
        key: "gender",
        width: 100
    },
    { title: "所在部门", dataIndex: "deptName", key: "deptName", width: 100 },
    { title: "卡号", dataIndex: "cardNo", key: "cardNo", width: 100 },
    {
        title: "卡片状态",
        dataIndex: "cardStatusName",
        key: "cardStatusName",
        width: 100
    }
])

const selectOpen = ref(false)
const selectTabs = [
    {
        tab: "教职工",
        key: 2,
        businessType: 20,
        code: null,
        single: true, // 单选多选
        checked: true
    },
    {
        tab: "学生",
        key: 1,
        businessType: 10,
        code: null,
        single: true, // 单选多选
        checked: true
    }
]

const handerConfirm = (data) => {
    personnelDataSource.value = data || []
    form.value.personId = data[0]?.id
    form.value.personName = data?.map((i) => i.name)?.join(",")
    selectOpen.value = false
}

function getInfo(item) {
    http.post("/unicard/mgmt/card-op-log/get-reissue", { id: item.id }).then(
        (res) => {
            form.value = res.data
            personnelDataSource.value = [
                {
                    ...res.data,
                    name: res.data?.personName,
                    id: res.data?.personId
                }
            ]
        }
    )
}

// 弹框确认
function submitModal() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        http.post("/unicard/mgmt/card-op-log/reissue", form.value)
            .then((res) => {
                message.success(res.message)
                cancel()
                emit("submitDrawer")
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}
async function getTypeList() {
    await http.post("/unicard/mgmt/consumption-type/list").then((res) => {
        typeList.value = res.data
    })
}
function getCardNumList() {
    http.get("/unicard/mgmt/card/inactive-list").then((res) => {
        cardNumList.value = res.data
    })
}

onMounted(async () => {
    await getTypeList()
    getCardNumList()
})

function open(type, item = {}) {
    modalType.value = type
    openModal.value = true
    if (type === "info") {
        getInfo(item)
    } else {
        form.value = {}
        personnelDataSource.value = []
    }
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.report_loss_content {
    padding: 20px;
}
</style>
