<template>
    <div>
        <YModal
            v-model:open="openModal"
            title="挂失"
            @cancel="cancel"
            :width="860"
            @confirm="submitModal"
        >
            <div class="report_loss_content">
                <a-form
                    :model="form"
                    ref="formRef"
                    :layout="modalType === 'add' ? 'vertical' : 'horizontal'"
                    :disabled="modalType === 'info'"
                >
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item
                                label="选择人员："
                                name="personName"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择人员'
                                    }
                                ]"
                            >
                                <a-input
                                    v-model:value.trim="form.personName"
                                    readOnly
                                    @click="() => (selectOpen = true)"
                                    placeholder="请选择"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-table
                                :columns="personnelColumns"
                                :scroll="{ x: 500 }"
                                :data-source="personnelDataSource"
                                :pagination="false"
                            >
                                <template #bodyCell="{ column, record }">
                                    <template
                                        v-if="column.dataIndex === 'gender'"
                                    >
                                        {{
                                            ["女", "男"][record.gender] ||
                                            "未知"
                                        }}
                                    </template>
                                </template>
                                <template #emptyText>
                                    <Empty
                                        :emptyStyle="{ marginBottom: '65px' }"
                                    ></Empty>
                                </template>
                            </a-table>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="挂失原因："
                                name="remark"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入挂失原因'
                                    }
                                ]"
                            >
                                <a-textarea
                                    v-model:value.trim="form.remark"
                                    placeholder="请输入"
                                    :auto-size="{ minRows: 10, maxRows: 10 }"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </YModal>
        <YSelect
            v-model:visible="selectOpen"
            :tabs="selectTabs"
            :maximum="100"
            :isPickType="['person']"
            @confirm="handerConfirm"
        />
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const emit = defineEmits(["submitDrawer"])
const modalType = ref("add")
const selectOpen = ref(false)
const selectTabs = [
    {
        tab: "教职工",
        key: 2,
        businessType: 20,
        code: null,
        single: true, // 单选多选
        checked: true
    },
    {
        tab: "学生",
        key: 1,
        businessType: 10,
        code: null,
        single: true, // 单选多选
        checked: true
    }
]
const openModal = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})
const personnelDataSource = ref([])

const personnelColumns = ref([
    { title: "工号", dataIndex: "jobNumber", key: "jobNumber", width: 100 },
    { title: "姓名", dataIndex: "name", key: "name", width: 100 },
    {
        title: "性别",
        dataIndex: "gender",
        key: "gender",
        width: 100
    },
    { title: "所在部门", dataIndex: "deptName", key: "deptName", width: 100 },
    { title: "卡号", dataIndex: "cardNo", key: "cardNo", width: 100 },
    {
        title: "卡片状态",
        dataIndex: "cardStatusName",
        key: "cardStatusName",
        width: 100
    }
])

// 弹框确认
function submitModal() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        http.post("/unicard/mgmt/card-op-log/lost", form.value)
            .then((res) => {
                message.success(res.message)
                cancel()
                emit("submitDrawer")
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}

const handerConfirm = (data) => {
    personnelDataSource.value = data || []
    form.value.personId = data[0]?.id
    form.value.personName = data?.map((i) => i.name)?.join(",")
    selectOpen.value = false
}

function open(type, item = {}) {
    modalType.value = type
    openModal.value = true
    if (type === "info") {
        form.value = item || {}
        personnelDataSource.value = [
            {
                ...item,
                name: item?.personName,
                id: item?.personId
            }
        ]
    } else {
        form.value = {}
        personnelDataSource.value = []
    }
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.report_loss_content {
    padding: 20px;
}
</style>
