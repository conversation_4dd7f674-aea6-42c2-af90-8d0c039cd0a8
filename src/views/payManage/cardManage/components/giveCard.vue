<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="发卡" @close="cancel">
            <div class="drawer_content">
                <div class="form_content">
                    <a-form :model="form" ref="formRef" layout="vertical">
                        <a-row :gutter="[24, 18]">
                            <a-col :span="24">
                                <a-form-item label="选择人员：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择人员' }]">
                                    <a-input v-model:value.trim="form.executor" readoly @click="() => (selectOpen = true)" placeholder="请选择" />
                                </a-form-item>
                            </a-col>
                            <a-col :span="24">
                                <a-form-item label="卡片类型：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择卡片类型' }]">
                                    <a-select
                                        ref="select"
                                        :disabled="true"
                                        v-model:value="form.isNonlocalStudent"
                                        placeholder="请选择"
                                        :options="[
                                            { label: 'IC电子卡', value: 1 },
                                            { label: 'IC电子卡', value: 0 }
                                        ]"
                                    ></a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="24">
                                <a-form-item label="消费类型：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择消费类型' }]">
                                    <a-select
                                        ref="select"
                                        type="multiple"
                                        v-model:value="form.isNonlocalStudent"
                                        placeholder="请选择（多选）"
                                        :options="[
                                            { label: 'IC电子卡', value: 1 },
                                            { label: 'IC电子卡', value: 0 }
                                        ]"
                                    ></a-select>
                                </a-form-item>
                            </a-col>
                            <a-col :span="24">
                                <a-form-item label="卡有效期：">
                                    <RangePicker v-model:startTime="form.startTime" v-model:endTime="form.endTime"></RangePicker>
                                </a-form-item>
                            </a-col>
                            <a-col :span="24">
                                <a-form-item label="卡片押金：" name="majorName">
                                    <a-input-number style="width: 100%" :step="1" :precision="2" v-model:value.trim="form.majorName" placeholder="请输入" addon-after="元"> </a-input-number>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>
                </div>
                <div class="preview_number">
                    <div class="preview_header">
                        <span class="title"><span class="required">*</span>卡号预览：</span>
                        <span class="number">100张</span>
                    </div>
                    <div class="preview_content">
                        <div class="number_list" v-if="numberList?.length">
                            <div class="number_item">卡号</div>
                            <div class="number_item" v-for="(item, index) in numberList" :key="index">
                                {{ item }}
                            </div>
                        </div>
                        <Empty v-else :emptyStyle="{ paddingTop: '20%' }" />
                    </div>
                </div>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YDrawer>
        <YSelect v-model:visible="selectOpen" :tabs="selectTabs" :selected="form.personListDTO" :maximum="10" :isPickType="'people_dept'" @confirm="handerConfirm" />
    </div>
</template>

<script setup>
import { message, Modal } from "ant-design-vue"
import { createVNode } from "vue"
import { ExclamationCircleFilled } from "@ant-design/icons-vue"
const emit = defineEmits(["submitDrawer"])
const drawerOpen = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})
const numberList = ref([])
const selectOpen = ref(false)
const selectTabs = [
    {
        tab: "教职工",
        key: 2,
        businessType: 21,
        code: null,
        single: false, // 单选多选
        checked: true
    }
]

const handerConfirm = (data) => {
    form.value.personListDTO = data?.map((i) => {
        return {
            ...i,
            identity: 1
        }
    })
    form.value.executor = data?.map((i) => i.name)?.join(",")
    selectOpen.value = false
    console.log(form.value, " form.value")
}
function submit() {
    formRef.value.validate().then(() => {
        Modal.confirm({
            title: "提示",
            icon: createVNode(ExclamationCircleFilled),
            content: "确定给以上人员发卡吗？",
            okText: "确 认",
            cancelText: "取 消",
            onOk() {
                submitLoading.value = true
                // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
                // http.post(url, form.value)
                //     .then((res) => {
                //         YMessage.success(res.message)
                //         cancel()
                //         emit("submitDrawer")
                //     })
                //     .finally(() => {
                //         submitLoading.value = false
                //     })
                emit("submitDrawer")
            },
            onCancel() {
                message.info("已取消发卡！")
            }
        })
    })
}
function open(item) {
    drawerOpen.value = true
    form.value = item || {}
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    display: flex;
    height: 100%;
    .form_content {
        flex: 1;
    }
    .preview_number {
        flex: 1;
        height: 100%;
        margin-left: 20px;
        background: #f8f8f8;
        padding: 20px;
        .preview_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .title {
                font-weight: 500;
                font-size: 14px;
                color: var(--text-color);
                line-height: 20px;
                .required {
                    color: var(--error-color);
                }
            }
            .number {
                font-weight: 500;
                font-size: 14px;
                color: var(--text-color);
                line-height: 20px;
            }
        }
        .preview_content {
            background: var(--bg-color);
            height: calc(100% - 50px);
            margin-top: 10px;
            width: 100%;
            max-height: calc(100% - 50px);
            overflow-y: auto;
        }
        .number_list {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            height: 100%;
            background: var(--bg-color);
            .number_item {
                padding: 10px 0px;
                font-weight: 400;
                width: 100%;
                text-align: center;
                font-size: 14px;
                color: var(--text-color);
                line-height: 20px;
                border-bottom: 1px solid #f8f8f8;
            }
        }
    }
}
</style>
