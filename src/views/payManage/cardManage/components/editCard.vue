<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="编辑" @close="cancel">
            <div class="drawer_content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="12">
                            <a-form-item
                                label="学号："
                                name="jobNumber"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入学号'
                                    }
                                ]"
                            >
                                <a-input
                                    :disabled="true"
                                    v-model:value.trim="form.jobNumber"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="姓名："
                                name="name"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入姓名'
                                    }
                                ]"
                            >
                                <a-input
                                    :disabled="true"
                                    v-model:value.trim="form.name"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="性别："
                                name="gender"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择性别'
                                    }
                                ]"
                            >
                                <a-select
                                    ref="select"
                                    :disabled="true"
                                    v-model:value="form.gender"
                                    placeholder="请选择"
                                    :options="[
                                        { label: '未知', value: 2 },
                                        { label: '男', value: 1 },
                                        { label: '女', value: 0 }
                                    ]"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="所在部门：" name="deptName">
                                <!--  :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入所在部门'
                                    }
                                ]" -->
                                <a-input
                                    :disabled="true"
                                    v-model:value.trim="form.deptName"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="卡号：" name="cardNo">
                                <!-- :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入卡号'
                                    }
                                ]" -->
                                <a-input
                                    :disabled="true"
                                    v-model:value.trim="form.cardNo"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="卡片状态："
                                name="cardStatusName"
                            >
                                <!-- :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择卡片状态'
                                    }
                                ]" -->
                                <a-input
                                    :disabled="true"
                                    v-model:value.trim="form.cardStatusName"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="余额：" name="balance">
                                <!-- :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入余额'
                                    }
                                ]" -->
                                <a-input-number
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    :min="0"
                                    :disabled="true"
                                    v-model:value.trim="form.balance"
                                    placeholder="请输入"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="卡片类型："
                                name="cardType"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择卡片类型'
                                    }
                                ]"
                            >
                                <a-select
                                    ref="select"
                                    :disabled="true"
                                    mode="multiple"
                                    v-model:value="form.cardType"
                                    placeholder="请选择"
                                    :options="[
                                        {
                                            label: 'IC电子卡',
                                            value: 'icElectronicCard'
                                        }
                                    ]"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="消费类型："
                                name="typeIds"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择消费类型'
                                    }
                                ]"
                            >
                                <a-select
                                    ref="select"
                                    mode="multiple"
                                    v-model:value="form.typeIds"
                                    placeholder="请选择"
                                    :options="typeList"
                                    :fieldNames="{
                                        label: 'typeName',
                                        value: 'id'
                                    }"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="卡有效期："
                                name="validityEndDate"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择卡有效期'
                                    }
                                ]"
                            >
                                <RangePicker
                                    v-model:startTime="form.validityStartDate"
                                    v-model:endTime="form.validityEndDate"
                                ></RangePicker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="卡片押金："
                                name="cashPledge"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请输入卡片押金'
                                    }
                                ]"
                            >
                                <a-input-number
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="form.cashPledge"
                                    placeholder="请输入"
                                    addon-after="元"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button
                    type="primary"
                    :loading="submitLoading"
                    @click="submit"
                    >确定</a-button
                >
            </template>
        </YDrawer>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const emit = defineEmits(["submitDrawer"])
const drawerOpen = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})
const typeList = ref([])

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        http.post("/unicard/mgmt/person/update", form.value)
            .then((res) => {
                message.success(res.message)
                cancel()
                emit("submitDrawer")
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}

async function getTypeList() {
    await http.post("/unicard/mgmt/consumption-type/list").then((res) => {
        typeList.value = res.data
    })
}

async function open(item) {
    drawerOpen.value = true
    await getTypeList()
    form.value = { ...item, cardType: "icElectronicCard" } || {
        cardType: "icElectronicCard"
    }
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
}
</style>
