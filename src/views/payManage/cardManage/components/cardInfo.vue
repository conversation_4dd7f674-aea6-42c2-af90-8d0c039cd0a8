<template>
    <div>
        <YDrawer
            v-model:open="drawerOpen"
            title="详情"
            @close="cancel"
            :footer="null"
        >
            <div class="drawer_content">
                <div class="title">
                    <div class="line"></div>
                    基本信息
                </div>
                <a-row :gutter="[24, 18]">
                    <a-col :span="8" v-for="item in infoLabel" :key="item.key">
                        {{ item.label }}
                        <span v-if="item.key === 'gender'">
                            {{ ["女", "男"][info[item.key]] || "未知" }}
                        </span>
                        <span v-else>{{ info[item.key] || "-" }}</span>
                    </a-col>
                </a-row>
                <div class="record">
                    <div class="record_title">
                        <div class="line"></div>
                        <span>卡片操作记录</span>
                    </div>
                    <div style="margin-top: 16px">
                        <ETable
                            :columns="columns"
                            :scroll="{ x: 900 }"
                            :data-source="dataSource"
                            :paginations="pagination"
                            :loading="tableLoading"
                            @change="handleTableChange"
                        >
                            <template #bodyCell="{ column, record, text }">
                                <template v-if="column.dataIndex === 'operate'">
                                    <a-button
                                        type="link"
                                        class="btn-link-color"
                                        @click="cardRecordInfoFn(record)"
                                        >详情</a-button
                                    >
                                </template>
                                <template v-else>
                                    <Tooltip
                                        :maxWidth="column.width"
                                        :title="text"
                                    ></Tooltip>
                                </template>
                            </template>
                        </ETable>
                    </div>
                </div>
            </div>
        </YDrawer>
        <lost-info ref="lostRef" />
        <un-lost-info ref="unLostRef" />
        <replaced-info ref="replacedRef" />
        <canceled-info ref="canceledRef" />
    </div>
</template>

<script setup>
import LostInfo from "./cardStatusInfo/lost.vue"
import UnLostInfo from "./cardStatusInfo/unLost.vue"
import ReplacedInfo from "./cardStatusInfo/replaced.vue"
import CanceledInfo from "./cardStatusInfo/canceled.vue"

const drawerOpen = ref(false)
const infoLabel = [
    {
        label: "学号：",
        key: "jobNumber"
    },
    {
        label: "姓名：",
        key: "name"
    },
    {
        label: "性别：",
        key: "gender"
    },
    {
        label: "所在部门：",
        key: "deptName"
    },
    {
        label: "卡片状态：",
        key: "cardStatus"
    },
    {
        label: "卡号：",
        key: "cardNo"
    },
    {
        label: "余额：",
        key: "balance"
    },
    {
        label: "卡片有效期：",
        key: "validityEndDate"
    },
    {
        label: "更新人：",
        key: "updateBy"
    },
    {
        label: "更新时间：",
        key: "updateTime"
    }
]
const info = ref({})
const dataSource = ref([])
const tableLoading = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = ref([
    { title: "卡号", dataIndex: "cardNo", key: "cardNo" },
    {
        title: "卡片操作",
        dataIndex: "operationTypeName",
        key: "operationTypeName"
    },
    {
        title: "操作详情",
        dataIndex: "remark",
        key: "remark"
    },
    { title: "操作人", dataIndex: "createBy", key: "createBy" },
    {
        title: "操作时间",
        dataIndex: "createTime",
        key: "createTime"
    },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

const lostRef = ref(null)
const unLostRef = ref(null)
const replacedRef = ref(null)
const canceledRef = ref(null)

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/card-op-log/page", pagination.value)
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

function cardRecordInfoFn(item) {
    switch (item.operationType) {
        case "lost":
            lostRef.value.open("info", item)
            break
        case "unLost":
            unLostRef.value.open("info", item)
            break
        case "replaced":
            replacedRef.value.open("info", item)
            break
        case "canceled":
            canceledRef.value.open("info", item)
            break

        default:
            break
    }
}

function open(item) {
    drawerOpen.value = true
    info.value = item || {}
    getInitList()
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
    .title {
        display: flex;
        align-items: center;
        margin-bottom: 14px;

        .line {
            width: 2px;
            height: 14px;
            background: var(--primary-color);
            margin-right: 4px;
        }
        font-weight: 500;
        font-size: 14px;
        color: var(--text-color);
        line-height: 20px;
    }
    .record {
        padding: 16px 0;
        margin: 16px 0;
        border-top: 1px dashed #d8d8d8;
        .record_title {
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 20px;
            .line {
                width: 2px;
                height: 14px;
                background: #00b781;
                margin-right: 4px;
            }
        }
    }
}
</style>
