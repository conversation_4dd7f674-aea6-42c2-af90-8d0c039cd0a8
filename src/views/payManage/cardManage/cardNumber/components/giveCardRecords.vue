<template>
    <div>
        <YDrawer
            v-model:open="drawerOpen"
            title="发卡记录"
            @close="cancel"
            :footer="null"
        >
            <div class="drawer_content">
                <a-radio-group
                    style="margin-bottom: 12px"
                    v-model:value="peopleType"
                    button-style="solid"
                    @change="reset"
                >
                    <a-radio-button value="teacher">
                        <div class="radio_item">老师</div>
                    </a-radio-button>
                    <a-radio-button value="student">
                        <div class="radio_item">学生</div>
                    </a-radio-button>
                </a-radio-group>
                <search-form
                    style="margin-bottom: 20px"
                    v-model:formState="query"
                    :formList="formList"
                    @submit="getInitList"
                    layout="horizontal"
                    @reset="reset"
                />
                <div class="btn_group">
                    <a-button>导出</a-button>
                </div>
                <ETable
                    :columns="columns"
                    :data-source="dataSource"
                    :paginations="pagination"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, text }">
                        <template v-if="column.dataIndex == 'gender'">
                            {{ ["女", "男"][record.gender] || "未知" }}
                        </template>
                        <template v-else>
                            <Tooltip
                                :maxWidth="column.width"
                                :title="text"
                            ></Tooltip>
                        </template>
                    </template>
                </ETable>
            </div>
        </YDrawer>
    </div>
</template>

<script setup>
import { computed } from "vue"
const drawerOpen = ref(false)
const peopleType = ref("teacher")
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = computed(() => {
    return [
        { title: "卡号", dataIndex: "cardNo", key: "cardNo" },
        {
            title: "姓名",
            dataIndex: "personName",
            key: "personName"
        },
        { title: "性别", dataIndex: "gender", key: "gender" },
        {
            title: peopleType.value === "teacher" ? "所在部门" : "所在班级",
            dataIndex: "deptName",
            key: "deptName"
        },
        {
            title: "发卡人",
            dataIndex: "createBy",
            key: "createBy"
        },
        {
            title: "发卡时间",
            dataIndex: "createTime",
            key: "createTime"
        }
    ]
})
const formList = computed(() => {
    return [
        {
            type: "input",
            value: "personName",
            label: "姓名"
        },
        {
            type: "input",
            value: "deptName",
            label: peopleType.value === "teacher" ? "所在部门" : "所在班级"
        },
        {
            type: "input",
            value: "cardNo",
            label: "卡号"
        },
        {
            type: "rangePicker",
            value: ["startCreateDate", "endCreateDate"],
            label: "发卡时间",
            attrs: {
                placeholder: ["开始时间", "结束时间"],
                valueFormat: "YYYY-MM-DD"
            }
        }
    ]
})
const peopleTypeObj = {
    teacher: 2,
    student: 1
}

function getList() {
    http.post("/unicard/mgmt/card-op-log/activated-page", {
        ...pagination.value,
        peopleType: peopleTypeObj[peopleType.value],
        ...query.value
    }).then((res) => {
        dataSource.value = res.data?.list
        pagination.value.pageNo = res.data?.pageNo
        pagination.value.pageSize = res.data?.pageSize
        pagination.value.total = res.data?.total
    })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}
function open() {
    drawerOpen.value = true
    reset()
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    // max-width: 1200px;
    // margin: auto;
    .btn_group {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 12px;
    }
    .radio_item {
        min-width: 100px;
        text-align: center;
    }
}
</style>
