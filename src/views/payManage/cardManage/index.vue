<template>
    <div>
        <div class="card_manage" v-if="route.path === '/payManage/cardManage'">
            <div class="left_people">
                <people-tree @changeKey="changeKey" ref="peopleTreeRef" />
            </div>
            <div class="right_content">
                <!-- 头部 -->
                <div class="card_head">
                    <span class="title">卡片操作记录</span>
                    <div class="btn_group">
                        <a-button type="primary" @click="giveCardFn"
                            >发卡</a-button
                        >
                        <a-button @click="cardNumFn">卡号管理</a-button>
                        <a-button @click="cardOperate">卡片操作</a-button>
                    </div>
                </div>
                <div class="content_page">
                    <!-- 搜索 -->
                    <search-form
                        style="margin-bottom: 20px"
                        v-model:formState="query"
                        :formList="formList"
                        @submit="getInitList"
                        layout="horizontal"
                        @reset="reset"
                    />
                    <div class="table_box">
                        <!-- 表格 -->
                        <ETable
                            :columns="columns"
                            :scroll="{ x: 1200 }"
                            :style="`max-width: ${tableWidth}`"
                            :data-source="dataSource"
                            :paginations="pagination"
                            @change="handleTableChange"
                            :loading="tableLoading"
                        >
                            <template
                                #bodyCell="{ column, record, index, text }"
                            >
                                <template v-if="column.dataIndex === 'index'">
                                    {{ index + 1 }}
                                </template>
                                <template v-if="column.dataIndex === 'gender'">
                                    {{ ["女", "男"][record.gender] || "未知" }}
                                </template>
                                <template
                                    v-else-if="column.dataIndex === 'operate'"
                                >
                                    <a-button
                                        type="link"
                                        class="btn-link-color"
                                        @click="cardInfoFn(record)"
                                        >详情</a-button
                                    >
                                    <a-button
                                        type="link"
                                        class="btn-link-color"
                                        @click="editCardFn(record)"
                                        >编辑</a-button
                                    >
                                </template>
                                <template v-else>
                                    <Tooltip
                                        :maxWidth="column.width"
                                        :title="text"
                                    ></Tooltip>
                                </template>
                            </template>
                        </ETable>
                    </div>
                </div>
            </div>

            <!-- 编辑卡片 -->
            <edit-card ref="editCardRef" @submitDrawer="reset" />

            <!-- 卡片详情 -->
            <card-info ref="cardInfoRef" />

            <!-- 发卡 -->
            <give-card ref="giveCardRef" />
        </div>
        <router-view></router-view>
    </div>
</template>

<script setup>
import useStore from "@/store"
import GiveCard from "./components/giveCard.vue"
import EditCard from "./components/editCard.vue"
import CardInfo from "./components/cardInfo.vue"
import PeopleTree from "./components/peopleTree.vue"
import { useRouter, useRoute } from "vue-router"

const { system } = useStore()
const tableWidth = computed(() => {
    return system.collapsed === "open"
        ? "calc(100vw - 526px)"
        : "calc(100vw - 400px)"
})

const peopleTreeRef = ref(null)
const router = useRouter()
const route = useRoute()
const query = ref({})
const dataSource = ref([])
const tableLoading = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const columns = computed(() => {
    return [
        { title: "工号", dataIndex: "jobNumber", key: "jobNumber", width: 100 },
        { title: "姓名", dataIndex: "name", key: "name", width: 100 },
        {
            title: "性别",
            dataIndex: "gender",
            key: "gender",
            width: 80
        },
        {
            title:
                peopleTreeRef.value?.peopleType === "teacher"
                    ? "所在部门"
                    : "所在班级",
            dataIndex: "deptName",
            key: "deptName",
            width: 140
        },
        {
            title: "用户状态",
            dataIndex: "statusName",
            key: "statusName",
            width: 100
        },
        {
            title: "卡片状态",
            dataIndex: "cardStatusName",
            key: "cardStatusName",
            width: 100
        },
        { title: "余额", dataIndex: "balance", key: "balance", width: 100 },
        {
            title: "使用期限",
            dataIndex: "validityStartDate",
            key: "validityStartDate",
            width: 160
        },
        {
            title: "更新时间",
            dataIndex: "updateTime",
            key: "updateTime",
            width: 160
        },
        { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
    ]
})

const cardStatusList = ref([])
const userStatusList = ref([])
const formList = computed(() => {
    return [
        {
            type: "input",
            value: "name",
            label: "姓名"
        },
        {
            type: "input",
            value: "deptName",
            label:
                peopleTreeRef.value?.peopleType === "teacher"
                    ? "所属部门"
                    : "所属班级"
        },
        {
            type: "select",
            value: "cardStatus",
            label: "卡片状态",
            list: cardStatusList.value,
            attrs: {
                fieldNames: {
                    label: "name",
                    value: "code"
                }
            }
        },
        {
            type: "input",
            value: "cardNo",
            label: "卡号"
        },
        {
            type: "select",
            value: "status",
            label: "用户状态",
            list: userStatusList.value,
            attrs: {
                fieldNames: {
                    label: "name",
                    value: "code"
                }
            }
        },
        {
            type: "rangePicker",
            value: ["startUpdateDate", "endUpdateDate"],
            label: "更新时间",
            attrs: {
                placeholder: ["开始时间", "结束时间"],
                valueFormat: "YYYY-MM-DD"
            }
        }
    ]
})

const editCardRef = ref(null)
const cardInfoRef = ref(null)
const giveCardRef = ref(null)

const peopleType = ref(2) // 人员类型
const peopleId = ref(null) // 树结构id
const personInfo = ref({})

// 发卡
function giveCardFn() {
    giveCardRef.value.open()
}

// 卡片操作
function cardOperate() {
    router.push({
        path: "/payManage/cardManage/cardOperate"
    })
}

// 卡号管理
function cardNumFn() {
    router.push({
        path: "/payManage/cardManage/cardNumber"
    })
}

// 编辑卡片
async function editCardFn(record) {
    await getInfo(record.id)
    editCardRef.value.open(personInfo.value)
}

// 卡片详情
async function cardInfoFn(record) {
    await getInfo(record.id)
    cardInfoRef.value.open(personInfo.value)
}

async function getInfo(id) {
    await http.post("/unicard/mgmt/person/get", { id }).then((res) => {
        personInfo.value = res.data || {}
    })
}

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/person/page", {
        ...pagination.value,
        ...query.value,
        id: peopleId.value,
        type: peopleType.value // 1 学生2老师
    })
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

const setType = () => {
    const obj = {
        teacher: 2,
        student: 1
    }
    peopleType.value = obj[peopleTreeRef.value?.peopleType]
}

function getCardStatus() {
    http.get("/unicard/mgmt/person/card-status").then((res) => {
        cardStatusList.value = res.data
    })
}

function getUserStatus() {
    http.get("/unicard/mgmt/person/user-status").then((res) => {
        userStatusList.value = res.data
    })
}

function changeKey(ids, item) {
    console.log("changeKey", ids, item)
    peopleId.value = ids
    setType()
    setTimeout(() => {
        reset()
    }, 100)
}
onMounted(() => {
    getCardStatus()
    getUserStatus()
    nextTick(async () => {
        setType()
        await peopleTreeRef.value?.getTreeData()
    })
})
</script>

<style lang="less" scoped>
.card_manage {
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;
    .left_people {
        min-width: 252px;
        width: 252px;
        min-height: 100%;
        border-right: 1px solid #d8d8d8;
    }
    .right_content {
        flex: 1;
        .card_head {
            width: 100%;
            padding: 16px 20px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-weight: 500;
                font-size: 18px;
                color: var(--text-color);
                line-height: 25px;
            }
            .btn_group {
                display: flex;
            }
        }
        .content_page {
            padding: 20px;
            width: 100%;
            .table_box {
                width: 100%;
            }
        }
    }
}
</style>
