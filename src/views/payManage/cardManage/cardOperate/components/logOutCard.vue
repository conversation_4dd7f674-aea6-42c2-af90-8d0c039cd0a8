<template>
    <div>
        <!-- 搜索 -->
        <search-form
            v-model:formState="query"
            :formList="formList"
            @submit="getInitList"
            layout="horizontal"
            @reset="reset"
        />
        <div class="btn_group">
            <a-button type="primary" @click="logOutCard('add')">注销</a-button>
        </div>
        <div class="table_box">
            <!-- 表格 -->
            <ETable
                :loading="tableLoading"
                :columns="columns"
                :scroll="{ x: 1400 }"
                :data-source="dataSource"
                :paginations="pagination"
                @change="handleTableChange"
            >
                <template #bodyCell="{ column, record, text }">
                    <template v-if="column.dataIndex === 'gender'">
                        {{ ["女", "男"][record.gender] || "未知" }}
                    </template>
                    <template v-else-if="column.dataIndex === 'payMethod'">
                        {{ { 10: "现金" }[record.payMethod] || "-" }}
                    </template>
                    <template v-else-if="column.dataIndex === 'sourceType'">
                        {{ { 1: "学生", 2: "老师" }[record.sourceType] || "-" }}
                    </template>
                    <template v-else-if="column.dataIndex === 'operate'">
                        <a-button
                            type="link"
                            class="btn-link-color"
                            @click="logOutCard('info', record)"
                            >详情</a-button
                        >
                    </template>
                    <template v-else>
                        <Tooltip
                            :maxWidth="column.width"
                            :title="text"
                        ></Tooltip>
                    </template>
                </template>
            </ETable>
        </div>
        <CanceledInfo ref="canceledInfoRef" @submitDrawer="reset" />
    </div>
</template>

<script setup>
import CanceledInfo from "../../components/cardStatusInfo/canceled.vue"
const query = ref({})
const dataSource = ref([])
const tableLoading = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const formList = ref([
    {
        type: "input",
        value: "personName",
        label: "姓名"
    },
    {
        type: "input",
        value: "deptName",
        label: "所属组织"
    },
    {
        type: "select",
        value: "peopleType",
        label: "人员类型",
        list: [
            { label: "全部", value: null },
            { label: "学生", value: 1 },
            { label: "老师", value: 0 }
        ]
    },
    {
        type: "rangePicker",
        value: ["startCreateDate", "endCreateDate"],
        label: "操作时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const columns = ref([
    {
        title: "工号/学号",
        dataIndex: "jobNumber",
        key: "jobNumber",
        width: 100
    },
    { title: "姓名", dataIndex: "personName", key: "personName", width: 100 },
    {
        title: "性别",
        dataIndex: "gender",
        key: "gender",
        width: 100
    },
    {
        title: "人员类型",
        dataIndex: "sourceType",
        key: "sourceType",
        width: 100
    },
    { title: "所在组织", dataIndex: "deptName", key: "deptName", width: 140 },
    { title: "卡号", dataIndex: "cardId", key: "cardId", width: 100 },
    {
        title: "卡片状态",
        dataIndex: "operationTypeName",
        key: "operationTypeName",
        width: 100
    },
    {
        title: "退款金额",
        dataIndex: "refundAmount",
        key: "refundAmount",
        width: 100
    },
    {
        title: "退回方式",
        dataIndex: "payMethod",
        key: "payMethod",
        width: 160
    },
    { title: "操作人", dataIndex: "createBy", key: "createBy", width: 160 },
    {
        title: "操作时间",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    { title: "操作", dataIndex: "operate", width: 100, fixed: "right" }
])

const canceledInfoRef = ref(null)
// 弹框取消
function cancelModal() {
    openModal.value = false
}

// 补办/换卡
function logOutCard(type, item = {}) {
    canceledInfoRef.value.open(type, item)
}

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/card-op-log/cancelled-page", {
        ...pagination.value,
        ...query.value
    })
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

defineExpose({ reset })
</script>

<style lang="less" scoped>
.btn_group {
    display: flex;
    justify-content: flex-end;
    margin: 20px 0;
}
</style>
