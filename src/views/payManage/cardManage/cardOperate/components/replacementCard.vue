<template>
    <div>
        <!-- 搜索 -->
        <search-form
            v-model:formState="query"
            :formList="formList"
            @submit="getInitList"
            layout="horizontal"
            @reset="reset"
        />
        <div class="btn_group">
            <a-button type="primary" @click="replacementCard('add')"
                >补办/换卡</a-button
            >
        </div>
        <div class="table_box">
            <!-- 表格 -->
            <ETable
                :columns="columns"
                :scroll="{ x: 1400 }"
                :data-source="dataSource"
                :paginations="pagination"
                :loading="tableLoading"
                @change="handleTableChange"
            >
                <template #bodyCell="{ column, record, text }">
                    <template v-if="column.dataIndex === 'gender'">
                        {{ ["女", "男"][record.gender] || "未知" }}
                    </template>
                    <template v-else-if="column.dataIndex === 'operate'">
                        <a-button
                            type="link"
                            class="btn-link-color"
                            @click="replacementCard('info', record)"
                            >详情</a-button
                        >
                    </template>
                    <template v-else>
                        <Tooltip
                            :maxWidth="column.width"
                            :title="text"
                        ></Tooltip>
                    </template>
                </template>
            </ETable>
        </div>
        <ReplacedInfo ref="replacedRef" @submitDrawer="reset" />
    </div>
</template>

<script setup>
import ReplacedInfo from "../../components/cardStatusInfo/replaced.vue"

const replacedRef = ref(null)
const query = ref({})
const dataSource = ref([])
const tableLoading = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const formList = ref([
    {
        type: "input",
        value: "personName",
        label: "姓名"
    },
    {
        type: "input",
        value: "deptName",
        label: "所属组织"
    },
    {
        type: "input",
        value: "cardNo",
        label: "挂失卡号"
    },
    {
        type: "input",
        value: "reissueCardNo",
        label: "补办卡号"
    },
    {
        type: "select",
        value: "peopleType",
        label: "人员类型",
        list: [
            { label: "全部", value: null },
            { label: "学生", value: 1 },
            { label: "老师", value: 2 }
        ]
    },
    {
        type: "rangePicker",
        value: ["startCreateDate", "endCreateDate"],
        label: "操作时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const columns = ref([
    { title: "工号", dataIndex: "jobNumber", key: "jobNumber", width: 100 },
    { title: "姓名", dataIndex: "personName", key: "personName", width: 100 },
    {
        title: "性别",
        dataIndex: "gender",
        key: "gender",
        width: 100
    },
    { title: "人员类型", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "所在组织", dataIndex: "deptName", key: "deptName", width: 140 },
    {
        title: "挂失卡号",
        dataIndex: "cardNo",
        key: "cardNo",
        width: 100
    },
    {
        title: "卡片状态",
        dataIndex: "operationTypeName",
        key: "operationTypeName",
        width: 100
    },
    {
        title: "原卡余额",
        dataIndex: "nowBalance",
        key: "nowBalance",
        width: 100
    },
    {
        title: "补办卡号",
        dataIndex: "reissueCardNo",
        key: "reissueCardNo",
        width: 100
    },
    {
        title: "补办卡号余额",
        dataIndex: "reissueBalance",
        key: "reissueBalance",
        width: 120
    },
    {
        title: "补办费用",
        dataIndex: "reissueAmount",
        key: "reissueAmount",
        width: 160
    },
    { title: "操作人", dataIndex: "createBy", key: "createBy", width: 160 },
    {
        title: "操作时间",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    { title: "操作", dataIndex: "operate", width: 100, fixed: "right" }
])

// 补办/换卡
function replacementCard(type, item = {}) {
    replacedRef.value.open(type, item)
}

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/card-op-log/reissue-page", {
        ...pagination.value,
        ...query.value
    })
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
async function reset() {
    query.value = {}
    getInitList()
}

defineExpose({ reset })
</script>

<style lang="less" scoped>
.btn_group {
    display: flex;
    justify-content: flex-end;
    margin: 20px 0;
}
</style>
