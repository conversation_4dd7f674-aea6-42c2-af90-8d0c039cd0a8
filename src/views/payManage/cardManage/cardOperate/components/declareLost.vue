<template>
    <div>
        <!-- 搜索 -->
        <search-form
            v-model:formState="query"
            :formList="formList"
            @submit="getInitList"
            layout="horizontal"
            @reset="reset"
        />
        <div class="btn_group">
            <a-button type="primary" @click="reportLoss">挂失</a-button>
        </div>
        <div class="table_box">
            <!-- 表格 -->
            <ETable
                :columns="columns"
                :scroll="{ x: 1400 }"
                :data-source="dataSource"
                :paginations="pagination"
                :loading="tableLoading"
                @change="handleTableChange"
            >
                <template #bodyCell="{ column, record, text }">
                    <template v-if="column.dataIndex === 'gender'">
                        {{ ["女", "男"][record.gender] || "未知" }}
                    </template>
                    <template v-else>
                        <Tooltip
                            :maxWidth="column.width"
                            :title="text"
                        ></Tooltip>
                    </template>
                </template>
            </ETable>
        </div>
        <lost-info ref="lostRef" @submitDrawer="reset" />
    </div>
</template>

<script setup>
import LostInfo from "../../components/cardStatusInfo/lost"
const lostRef = ref(null)
const query = ref({})
const dataSource = ref([])
const tableLoading = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = ref([
    {
        title: "工号/学号",
        dataIndex: "jobNumber",
        key: "jobNumber",
        width: 100
    },

    { title: "姓名", dataIndex: "personName", key: "personName", width: 100 },
    {
        title: "性别",
        dataIndex: "gender",
        key: "gender",
        width: 100
    },
    {
        title: "人员类型",
        dataIndex: "personName",
        key: "personName",
        width: 100
    },
    { title: "所在组织", dataIndex: "deptName", key: "deptName", width: 140 },
    { title: "卡号", dataIndex: "cardId", key: "cardId", width: 100 },
    {
        title: "卡片状态",
        dataIndex: "operationTypeName",
        key: "operationTypeName",
        width: 100
    },
    {
        title: "渠道",
        dataIndex: "operationChannel",
        key: "operationChannel",
        width: 120
    },
    { title: "原因", dataIndex: "remark", key: "remark", width: 160 },
    { title: "操作人", dataIndex: "updateBy", key: "updateBy", width: 160 },
    {
        title: "操作时间",
        dataIndex: "updateTime",
        key: "updateTime",
        width: 160
    }
])
const formList = ref([
    {
        type: "input",
        value: "personName",
        label: "姓名"
    },
    {
        type: "input",
        value: "deptName",
        label: "所属组织"
    },
    {
        type: "select",
        value: "peopleType",
        label: "人员类型",
        list: [
            { label: "全部", value: null },
            { label: "学生", value: 1 },
            { label: "老师", value: 2 }
        ]
    },
    {
        type: "input",
        value: "cardNo",
        label: "卡号"
    },
    {
        type: "rangePicker",
        value: ["startCreateDate", "endCreateDate"],
        label: "操作时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

// 挂失
function reportLoss() {
    lostRef.value.open("add")
}

function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/card-op-log/lost-page", {
        ...pagination.value,
        ...query.value
    })
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

defineExpose({ reset })
</script>

<style lang="less" scoped>
.btn_group {
    display: flex;
    justify-content: flex-end;
    margin: 20px 0;
}
</style>
