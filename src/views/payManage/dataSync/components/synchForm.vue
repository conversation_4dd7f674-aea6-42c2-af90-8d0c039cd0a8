<template>
    <div>
        <YModal
            v-model:open="openModal"
            title="一键同步"
            @close="cancel"
            @cancel="cancel"
            @confirm="submit"
        >
            <div class="content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="12">
                            <a-form-item
                                label="同步类型："
                                name="majorName"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择同步类型'
                                    }
                                ]"
                            >
                                <a-checkbox-group
                                    v-model:value="form.majorNameList"
                                    :options="typeOptions"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button
                    type="primary"
                    :loading="submitLoading"
                    @click="submit"
                    >确定</a-button
                >
            </template>
        </YModal>
    </div>
</template>

<script setup>
const emit = defineEmits(["submitDrawer"])
const openModal = ref(false)
const submitLoading = ref(false)

const formRef = ref(null)
const form = ref({})

const typeOptions = ref([
    {
        label: "人脸",
        value: "miniprogram"
    },
    {
        label: "卡号",
        value: "wxpay"
    }
])
function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
        // http.post(url, form.value)
        //     .then((res) => {
        //         YMessage.success(res.message)
        //         cancel()
        //         emit("submitDrawer")
        //     })
        //     .finally(() => {
        //         submitLoading.value = false
        //     })
        emit("submitDrawer")
    })
}

function open() {
    openModal.value = true
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
}
</style>
