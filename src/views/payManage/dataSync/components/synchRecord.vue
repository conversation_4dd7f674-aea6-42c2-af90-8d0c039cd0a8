<template>
    <div>
        <YModal
            v-model:open="openModal"
            title="一键同步"
            @close="cancel"
            @cancel="cancel"
            :footer="null"
            :width="800"
        >
            <div class="content">
                <ETable
                    :columns="columns"
                    :scroll="{ x: 700 }"
                    :data-source="dataSource"
                    :paginations="pagination"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{
                            record.index + index + 1
                        }}</template>
                    </template>
                </ETable>
            </div>
        </YModal>
    </div>
</template>

<script setup>
const openModal = ref(false)
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const columns = ref([
    { title: "序号", dataIndex: "index", width: 100 },
    { title: "设备名称", dataIndex: "index", width: 100 },
    { title: "姓名", dataIndex: "majorName", key: "majorName" },
    {
        title: "人脸同步状态",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    {
        title: "卡号同步状态",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    { title: "操作人", dataIndex: "createTime", key: "createTime" },
    { title: "下发时间", dataIndex: "createTime", key: "createTime" }
])

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function open() {
    openModal.value = true
    getInitList()
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
}
</style>
