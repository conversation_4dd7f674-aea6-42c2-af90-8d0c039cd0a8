<template>
    <div class="device_list">
        <div class="operate">
            <!-- 标题  -->
            <div class="device_title">
                <span class="title">设备列表</span>
                <span class="quantity">共50台</span>
            </div>

            <!-- 说明 -->
            <div class="tip">说明：只能同步数据在线的设备</div>

            <!-- 搜索 -->
            <div class="search">
                <a-input
                    v-model:value="searchData"
                    placeholder="请输入设备名称"
                >
                    <template #suffix>
                        <SearchOutlined />
                    </template>
                </a-input>
            </div>

            <!-- 设备类型 -->
            <div class="device_type">
                <span>设备类型：</span>
                <a-select
                    v-model:value="deviceType"
                    style="width: 100%; flex: 1"
                    :options="typeOptions"
                    placeholder="请选择"
                >
                </a-select>
            </div>

            <!-- 按钮 -->
            <div class="operate_btn">
                <a-button type="primary">批量关联</a-button>
                <a-button type="primary">批量同步</a-button>
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="list">
            <a-checkbox-group v-model:value="selectDevice">
                <a-row :gutter="[24]">
                    <a-col :span="24" v-for="item in list" :key="item.name"
                        ><div
                            :class="{
                                active: selectDevice.includes(item.name)
                            }"
                        >
                            <a-checkbox :value="item.name">
                                <div class="device_item">
                                    <div class="title_box">
                                        <div class="title">
                                            <div class="status"></div>
                                            消费机｜食堂1团餐机
                                        </div>
                                        <span
                                            >20<span style="color: #b7b7b7"
                                                >/300</span
                                            >
                                        </span>
                                    </div>
                                    <span class="number"
                                        >IMEI：49988888888888</span
                                    >
                                </div>
                            </a-checkbox>
                        </div>
                    </a-col>
                </a-row>
            </a-checkbox-group>
        </div>
    </div>
</template>

<script setup>
const searchData = ref("")
const deviceType = ref(null)
const typeOptions = [
    {
        value: "1",
        label: "全部"
    },
    {
        value: "2",
        label: "设备1"
    },
    {
        value: "3",
        label: "设备2"
    }
]
const selectDevice = ref([])

const list = ref([
    {
        name: "设备1",
        type: "设备1",
        status: "在线"
    },
    {
        name: "设备2",
        type: "设备2",
        status: "在线"
    },
    {
        name: "设备3",
        type: "设备3",
        status: "在线"
    },
    {
        name: "设备4",
        type: "设备4",
        status: "在线"
    },
    {
        name: "设备5",
        type: "设备5",
        status: "在线"
    },
    {
        name: "设备6",
        type: "设备6",
        status: "在线"
    },
    {
        name: "设备7",
        type: "设备7",
        status: "在线"
    },
    {
        name: "设备8",
        type: "设备8",
        status: "在线"
    },
    {
        name: "设备9",
        type: "设备9",
        status: "在线"
    },
    {
        name: "设备10",
        type: "设备10",
        status: "在线"
    },
    {
        name: "设备11",
        type: "设备11",
        status: "在线"
    },
    {
        name: "设备12",
        type: "设备12",
        status: "在线"
    },
    {
        name: "设备13",
        type: "设备13",
        status: "在线"
    },
    {
        name: "设备14",
        type: "设备14",
        status: "在线"
    },
    {
        name: "设备15",
        type: "设备15",
        status: "在线"
    },
    {
        name: "设备16"
    }
])
</script>

<style lang="less" scoped>
.device_list {
    padding: 16px 0px;
    .operate {
        padding: 0px 12px;
        .device_title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            color: var(--text-color);
            line-height: 20px;
        }
        .tip {
            width: 100%;
            height: 22px;
            margin: 12px 0;
            background: #ffecd6;
            border-radius: 4px;
            font-weight: 400;
            font-size: 12px;
            color: var(--warning-color);
            line-height: 22px;
            padding: 0px 8px;
        }
        .search {
            margin-bottom: 16px;
        }
        .device_type {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        .operate_btn {
            display: flex;
            align-items: center;
            .ant-btn {
                flex: 1;
            }
        }
    }
    .list {
        max-height: calc(100vh - 346px);
        padding: 0px 8px;
        overflow-y: auto;
        overflow-x: hidden;
        :deep(.ant-checkbox-wrapper) {
            width: 100%;
            padding: 12px 4px;
            border-bottom: 1px solid #d8d8d8;
        }
        :deep(.ant-checkbox + span) {
            padding-right: 0 !important;
            width: 100%;
        }
        :deep(.ant-checkbox) {
            align-self: flex-start;
            margin-top: 6px;
        }
        .device_item {
            display: flex;
            flex-direction: column;
            .title_box {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
                .title {
                    display: flex;
                    align-items: center;
                    font-weight: 400;
                    font-size: 14px;
                    color: var(--text-color);
                    line-height: 20px;
                    .status {
                        width: 6px;
                        height: 6px;
                        background: var(--primary-color);
                        border-radius: 50%;
                        margin-right: 4px;
                    }
                }
            }
            .number {
                font-weight: 400;
                font-size: 12px;
                color: #999999;
                line-height: 17px;
            }
        }
    }
    .active {
        :deep(.ant-checkbox-wrapper) {
            background: var(--primary-color-bg) !important;
        }
    }
}
</style>
