<template>
    <div class="data_sync">
        <div class="left_device">
            <device-list />
        </div>
        <div class="user_content">
            <div class="data_head">卡片操作记录</div>
            <div class="content_page">
                <!-- 搜索 -->
                <search-form
                    v-model:formState="query"
                    :formList="formList"
                    @submit="getInitList"
                    layout="horizontal"
                    @reset="reset"
                />
                <div class="btn_group">
                    <a-button>关联用户信息</a-button>
                    <a-button @click="synch">一键同步</a-button>
                    <a-button @click="record">同步记录</a-button>
                    <a-button danger ghost>清 空</a-button>
                </div>
                <ETable
                    :columns="columns"
                    :data-source="dataSource"
                    :paginations="pagination"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{
                            index + 1
                        }}</template>
                        <template v-else-if="column.dataIndex == 'operate'">
                            <a-button type="link" class="btn-link-color"
                                >{{ record.index }}同步</a-button
                            >
                            <a-button
                                type="link"
                                danger
                                ghost
                                @click="delectUser(record)"
                                >删除</a-button
                            >
                        </template>
                    </template>
                </ETable>
            </div>
        </div>
        <!-- 一键同步 -->
        <synch-form ref="synchFormRef" />
        <!-- 同步记录 -->
        <synch-record ref="synchRecordRef" />
    </div>
</template>

<script setup>
import { message, Modal } from "ant-design-vue"
import { createVNode } from "vue"
import { ExclamationCircleFilled } from "@ant-design/icons-vue"

import SynchForm from "./components/synchForm.vue"
import DeviceList from "./components/deviceList.vue"
import SynchRecord from "./components/synchRecord.vue"

const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const columns = ref([
    { title: "序号", dataIndex: "index", width: 100 },
    { title: "ID", dataIndex: "index", width: 100 },
    { title: "姓名", dataIndex: "majorName", key: "majorName", width: 100 },
    {
        title: "性别",
        dataIndex: "educationalSystem",
        key: "educationalSystem",
        width: 100
    },
    { title: "所在组织", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "IC卡号", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "人脸", dataIndex: "createTime", key: "createTime", width: 100 },
    {
        title: "人脸同步状态",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    {
        title: "卡号同步状态",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    {
        title: "操作人员",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    {
        title: "同步时间",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

const formList = ref([
    {
        type: "input",
        value: "majorName",
        label: "姓名"
    },
    {
        type: "select",
        value: "machineStatus",
        label: "同步状态",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    }
])

const synchFormRef = ref(null)
const synchRecordRef = ref(null)

// 一键同步
function synch() {
    synchFormRef.value.open()
}

// 删除
function delectUser(item) {
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "确定删除吗？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
            // http.post(url, form.value)
            //     .then((res) => {
            //         YMessage.success(res.message)
            //         cancel()
            //         emit("submitDrawer")
            //     })
            //     .finally(() => {
            //         submitLoading.value = false
            //     })
            console.log(item)
        },
        onCancel() {
            message.info("已取消！")
        }
    })
}

// 同步记录
function record() {
    synchRecordRef.value.open()
}

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.data_sync {
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;
    .left_device {
        min-width: 252px;
        width: 252px;
        min-height: 100%;
        border-right: 1px solid #d8d8d8;
    }
    .user_content {
        flex: 1;
        .data_head {
            width: 100%;
            padding: 16px 20px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 500;
            font-size: 18px;
            color: var(--text-color);
            line-height: 25px;
        }
        .content_page {
            padding: 20px;
            width: 100%;
            .btn_group {
                display: flex;
                justify-content: flex-end;
                margin: 20px 0;
            }
        }
    }
}
.y-modal-container {
    overflow-y: auto;
    max-height: 606px;
    padding: 20px;
}
</style>
