<template>
    <!-- 充值记录 -->
    <div class="recharge_record">
        <a-tabs v-model:activeKey="peopleType" size="large">
            <a-tab-pane key="teacher" tab="老师"></a-tab-pane>
            <a-tab-pane key="student" tab="学生"></a-tab-pane>
        </a-tabs>
        <div class="page_content">
            <search-form
                v-model:formState="query"
                :formList="formList"
                @submit="getInitList"
                layout="horizontal"
                @reset="reset"
            />
            <div class="table_box">
                <ETable
                    :scroll="{ x: 1200 }"
                    :columns="columns"
                    :data-source="dataSource"
                    :paginations="pagination"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{
                            index + 1
                        }}</template>
                        <template v-else-if="column.dataIndex == 'majorName'">{{
                            record.majorName
                        }}</template>
                        <template v-else-if="column.dataIndex == 'operate'">
                            <a-button
                                type="link"
                                class="btn-link-color"
                                @click="refundInfoFn(record)"
                                >退款详情</a-button
                            >
                        </template>
                    </template>
                </ETable>
            </div>
        </div>
        <refund-info ref="refundInfoRef" />
    </div>
</template>

<script setup>
import RefundInfo from "../components/refundInfo.vue"
import { computed } from "vue"

const peopleType = ref("teacher")

const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = computed(() => {
    return [
        {
            title: peopleType.value === "teacher" ? "工号" : "学号",
            dataIndex: "index",
            width: 100
        },
        { title: "姓名", dataIndex: "majorName", key: "majorName", width: 100 },
        {
            title: "性别",
            dataIndex: "educationalSystem",
            key: "educationalSystem",
            width: 100
        },
        {
            title: peopleType.value === "teacher" ? "所在部门" : "所在班级",
            dataIndex: "createBy",
            key: "createBy",
            width: 100
        },
        { title: "卡号", dataIndex: "index", width: 100 },
        { title: "卡内余额", dataIndex: "index", width: 100 },
        {
            title: "退回金额",
            dataIndex: "createTime",
            key: "createTime",
            width: 100
        },
        {
            title: "退回方式",
            dataIndex: "createTime",
            key: "createTime",
            width: 130
        },
        {
            title: "退款状态",
            dataIndex: "createTime",
            key: "createTime",
            width: 100
        },
        {
            title: "退款人",
            dataIndex: "createTime",
            key: "createTime",
            width: 160
        },
        {
            title: "退款时间",
            dataIndex: "createTime",
            key: "createTime",
            width: 160
        },
        { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
    ]
})
const formList = computed(() => {
    return [
        {
            type: "input",
            value: "majorName",
            label: "姓名"
        },
        {
            type: "input",
            value: "majorName",
            label: peopleType.value === "teacher" ? "所属部门" : "所属班级"
        },
        {
            type: "input",
            value: "majorName",
            label: "卡号"
        },
        {
            type: "rangePicker",
            value: ["startTime", "endTime"],
            label: "退款时间",
            attrs: {
                placeholder: ["开始日期", "结束日期"],
                valueFormat: "YYYY-MM-DD"
            }
        }
    ]
})

const refundInfoRef = ref(null)

function refundInfoFn() {
    refundInfoRef.value.open()
}

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.recharge_record {
    :deep(.ant-tabs-nav-wrap) {
        padding-left: 20px;
    }
    :deep(.ant-tabs-tab) {
        padding: 16px 10px !important;
        font-weight: 500;
    }

    :deep(.ant-tabs-ink-bar) {
        height: 3px !important;
    }
    .page_content {
        padding: 10px 20px;
        .table_box {
            margin-top: 20px;
        }
    }
}
</style>
