<template>
    <div class="profile-container">
        <a-card title="用户设置" style="width: 100%">
            <a-space direction="vertical" size="large" style="width: 100%">
                <!-- 用户信息展示 -->
                <a-descriptions title="基本信息" bordered>
                    <a-descriptions-item label="用户名">{{
                        userInfo.username || "暂无"
                    }}</a-descriptions-item>
                    <a-descriptions-item label="手机号">{{
                        userInfo.phone || "暂无"
                    }}</a-descriptions-item>
                    <a-descriptions-item label="邮箱">{{
                        userInfo.email || "暂无"
                    }}</a-descriptions-item>
                    <a-descriptions-item label="创建时间">{{
                        userInfo.createTime || "暂无"
                    }}</a-descriptions-item>
                </a-descriptions>

                <!-- 操作按钮 -->
                <a-space>
                    <a-button type="primary" @click="showChangePasswordModal">
                        修改密码
                    </a-button>
                    <a-button @click="showForgetPasswordModal">
                        忘记密码
                    </a-button>
                </a-space>
            </a-space>
        </a-card>

        <!-- 修改密码弹窗组件 -->
        <ChangePasswordModal
            v-model:open="changePasswordModalVisible"
            :scene="passwordModalScene"
            @success="onPasswordChangeSuccess"
            @cancel="onPasswordChangeCancel"
        />
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue"
import { message } from "ant-design-vue"
import ChangePasswordModal from "@/components/ChangePasswordModal/index.vue"
import useStore from "@/store"

// 获取用户store
const { user } = useStore()

// 用户信息
const userInfo = reactive({
    username: "",
    phone: "",
    email: "",
    createTime: ""
})

// 修改密码弹窗相关状态
const changePasswordModalVisible = ref(false)
const passwordModalScene = ref("change") // 'initial', 'forget', 'change'

// 显示修改密码弹窗
const showChangePasswordModal = () => {
    passwordModalScene.value = "change"
    changePasswordModalVisible.value = true
}

// 显示忘记密码弹窗
const showForgetPasswordModal = () => {
    passwordModalScene.value = "forget"
    changePasswordModalVisible.value = true
}

// 密码修改成功回调
const onPasswordChangeSuccess = () => {
    message.success("密码修改成功，请重新登录")
}

// 密码修改取消回调
const onPasswordChangeCancel = () => {
    console.log("密码修改取消")
}

// 获取用户信息
const getUserInfo = () => {
    // 从store中获取用户信息
    const storeUserInfo = user.getUser
    if (storeUserInfo) {
        Object.assign(userInfo, storeUserInfo)
    }
}

// 组件挂载时获取用户信息
onMounted(() => {
    getUserInfo()
})
</script>

<style lang="less" scoped>
.profile-container {
    padding: 24px;
    background: #f0f2f5;
    min-height: 100vh;
}

:deep(.ant-descriptions-title) {
    font-weight: 600;
}
</style>
