<template>
    <div class="login">
        <div class="login-container">
            <div class="login-left">
                <div class="illustration">
                    <img
                        src="@/assets/images/<EMAIL>"
                        alt="illustration"
                    />
                </div>
            </div>
            <div class="login-right">
                <div class="login-form-container">
                    <div class="login-header">
                        <h2>欢迎登录一卡通</h2>
                    </div>
                    <a-form class="login-form">
                        <a-form-item
                            class="login-form-item"
                            v-bind="validateInfos.username"
                        >
                            <a-input
                                size="large"
                                placeholder="请输入用户名或手机号码"
                                v-model:value="modelRef.username"
                                class="login-input"
                            />
                        </a-form-item>
                        <a-form-item
                            class="login-form-item"
                            v-bind="validateInfos.password"
                        >
                            <a-input-password
                                size="large"
                                placeholder="请输入密码"
                                v-model:value="modelRef.password"
                                class="login-input"
                            />
                        </a-form-item>
                        <a-form-item>
                            <div class="login-options">
                                <a-checkbox v-model:checked="state.remember"
                                    >记住账号</a-checkbox
                                >
                                <!-- <span
                                    class="forget-password"
                                    @click="showForgetPasswordModal"
                                    >忘记密码</span
                                > -->
                            </div>
                        </a-form-item>
                        <a-form-item>
                            <a-button
                                size="large"
                                type="primary"
                                @click="submit"
                                class="login-button"
                                :loading="state.loginBtn"
                                :disabled="state.loginBtn"
                            >
                                登录
                            </a-button>
                        </a-form-item>
                        <a-form-item>
                            <div
                                class="agreement-section"
                                :class="{ shake: state.showShake }"
                            >
                                <a-checkbox
                                    v-model:checked="state.agreeTerms"
                                    @change="onAgreementChange"
                                >
                                    我已阅读并同意
                                </a-checkbox>
                                <a href="#" class="agreement-link"
                                    >《用户协议》</a
                                >
                                <span>、</span>
                                <a href="#" class="agreement-link"
                                    >《隐私协议》</a
                                >
                            </div>
                        </a-form-item>
                    </a-form>
                </div>
            </div>
        </div>
        <footer class="footer">
            <div class="footer-content">
                版权所有©深圳市一卡通科技有限公司　备案号{{ filingNo }}
            </div>
        </footer>

        <!-- 修改密码弹窗组件 -->
        <ChangePasswordModal
            v-model:open="changePasswordModalVisible"
            :scene="passwordModalScene"
            @success="onPasswordChangeSuccess"
            @cancel="onPasswordChangeCancel"
        />
    </div>
</template>

<script setup>
import { reactive, h, ref } from "vue"
import {
    UserOutlined,
    LockOutlined,
    SmileOutlined
} from "@ant-design/icons-vue"
import RSA from "@/utils/rsa.js"
import { Form } from "ant-design-vue"
import config from "../../../config"
import useStore from "@/store"
import { useRouter } from "vue-router"
import { notification, message } from "ant-design-vue"
import http from "@/utils/http"
import ChangePasswordModal from "@/components/ChangePasswordModal/index.vue"
const router = useRouter()
const { user } = useStore()

const { curVersion, title, filingNo, copyright, description } = config
const useForm = Form.useForm

const state = reactive({
    loginBtn: false,
    remember: false,
    agreeTerms: localStorage.getItem("agreeTerms") === "true" || false,
    showShake: false
})

// 修改密码弹窗相关状态
const changePasswordModalVisible = ref(false)
const passwordModalScene = ref("initial") // 'initial', 'forget', 'change'

const modelRef = reactive({
    username: "",
    password: ""
})

const rulesRef = reactive({
    username: [
        {
            required: true,
            message: "请输入账号"
        }
    ],
    password: [
        {
            required: true,
            message: "请输入密码"
        }
    ]
})

// resetFields
const { validate, validateInfos } = useForm(modelRef, rulesRef)

function timeFix() {
    const time = new Date()
    const hour = time.getHours()
    return hour < 9
        ? "早上好"
        : hour <= 11
        ? "上午好"
        : hour <= 13
        ? "中午好"
        : hour < 20
        ? "下午好"
        : "晚上好"
}
async function submit() {
    try {
        // 表单验证
        await validate()

        // 检查是否同意协议
        if (!state.agreeTerms) {
            // 触发抖动动画
            state.showShake = true
            setTimeout(() => {
                state.showShake = false
            }, 600)
            message.warning("请先同意用户协议和隐私协议")
            return
        }

        state.loginBtn = true

        // 调用登录接口获取token
        const loginRes = await getLogin()

        // 将token存储到localStorage和store中
        const token = loginRes.data.accessToken
        localStorage.setItem("token", token)
        user.setToken(token)

        // 检查用户状态
        const userStatusRes = await checkUserLogin()
        const userStatus = userStatusRes.data.status

        if (userStatus === 1) {
            // 状态正常，获取用户信息
            const userInfoRes = await getUserinfo()
            user.setupInfo(userInfoRes.data)

            // 登录成功，跳转到首页
            await router.replace({
                path: "/"
            })

            notification.open({
                message: "欢迎",
                description: `${timeFix()}，欢迎回来`,
                icon: () => h(SmileOutlined, { style: "color: #108ee9" })
            })
        } else if (userStatus === 3) {
            // 初始化密码状态，需要修改密码
            message.warning("检测到您是首次登录，需要修改初始密码")
            showChangePasswordModal()
        } else {
            message.error("用户状态异常，请联系管理员")
        }
    } catch (err) {
        console.log("登录错误:", err)
        message.error(err.message || "登录失败，请检查账号密码")
    } finally {
        state.loginBtn = false
    }
}

// 登录接口 - 获取token
const getLogin = () => {
    const paramEncipherObj = {
        grant_type: "password",
        client_id: "yide-partner",
        client_secret: "yide1234567",
        username: modelRef.username,
        password: modelRef.password
    }
    return http.postForm("/auth/oauth/token", {
        paramEncipher: RSA.encrypt(JSON.stringify(paramEncipherObj))
    })
}

// 检查用户登录状态接口
const checkUserLogin = () => {
    return http.get("/unicard/admin-user/checkUserLogin")
}

// 获取用户信息接口
const getUserinfo = () => {
    return http.get("/unicard/admin-user/login")
}

// 显示修改密码弹窗 - 初始密码修改
const showChangePasswordModal = () => {
    passwordModalScene.value = "initial"
    changePasswordModalVisible.value = true
}

// 显示忘记密码弹窗
const showForgetPasswordModal = () => {
    passwordModalScene.value = "forget"
    changePasswordModalVisible.value = true
}

// 密码修改成功回调
const onPasswordChangeSuccess = () => {
    console.log("密码修改成功")
}

// 密码修改取消回调
const onPasswordChangeCancel = () => {
    console.log("密码修改取消")
}

// 协议勾选状态变化处理
const onAgreementChange = (checked) => {
    localStorage.setItem("agreeTerms", checked.toString())
}
</script>

<style lang="less" scoped>
.login {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-image: url("@/assets/images/<EMAIL>");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;

    .login-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 50px 20px;
        min-height: calc(100vh - 80px);

        .login-left {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 600px;

            .illustration {
                text-align: center;

                img {
                    max-width: 100%;
                    height: auto;
                    max-height: 400px;
                }
            }
        }

        .login-right {
            flex: 0 0 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 250px;

            .login-form-container {
                background: white;
                border-radius: 12px;
                padding: 40px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
                width: 100%;
                max-width: 400px;

                .login-header {
                    text-align: center;
                    margin-bottom: 32px;

                    h2 {
                        font-size: 24px;
                        font-weight: 600;
                        color: #333;
                        margin: 0;
                    }
                }

                .login-form {
                    .login-form-item {
                        // height: 40px;
                        // border-radius: 8px;
                        // border: 1px solid #d9d9d9;
                        margin-bottom: 30px;

                        // &:focus,
                        // &:hover {
                        //     border-color: #04b578;
                        // }

                        .ant-input {
                            height: 40px;
                            font-size: 14px;
                        }

                        .ant-input-password {
                            height: 40px;

                            .ant-input {
                                height: 40px;
                            }
                        }
                    }

                    .login-options {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 24px;

                        .forget-password {
                            color: #1890ff;
                            cursor: pointer;
                            font-size: 14px;

                            &:hover {
                                color: #40a9ff;
                            }
                        }
                    }

                    .login-button {
                        width: 100%;
                        height: 50px;
                        background: #52c41a;
                        border: none;
                        border-radius: 8px;
                        font-size: 16px;
                        font-weight: 500;
                        margin-bottom: 24px;

                        &:hover {
                            background: #73d13d;
                        }

                        &:disabled {
                            background: #f5f5f5;
                            color: #bfbfbf;
                        }
                    }

                    .agreement-section {
                        text-align: center;
                        font-size: 12px;
                        color: #666;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-wrap: wrap;

                        .ant-checkbox-wrapper {
                            margin-right: 4px;
                        }

                        .agreement-link {
                            color: #1890ff;
                            text-decoration: none;

                            &:hover {
                                color: #40a9ff;
                                text-decoration: underline;
                            }
                        }

                        &.shake {
                            animation: shake 0.6s ease-in-out;
                        }
                    }
                }
            }
        }
    }

    .footer {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);

        .footer-content {
            color: rgba(102, 102, 102, 0.88);
            font-size: 12px;
            text-align: center;
        }
    }
}

@keyframes shake {
    0%,
    100% {
        transform: translateX(0);
    }
    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateX(-5px);
    }
    20%,
    40%,
    60%,
    80% {
        transform: translateX(5px);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .login {
        .login-container {
            flex-direction: column;
            padding: 20px;

            .login-left {
                margin-bottom: 30px;

                .illustration {
                    img {
                        max-height: 200px;
                    }
                }
            }

            .login-right {
                flex: none;
                margin-left: 0;
                width: 100%;

                .login-form-container {
                    padding: 30px 20px;
                }
            }
        }
    }
}
</style>
