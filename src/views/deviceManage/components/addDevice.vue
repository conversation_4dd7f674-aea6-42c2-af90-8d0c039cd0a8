<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="添加设备" @close="cancel">
            <div class="drawer_content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item label="设备类型：" name="deviceType" :rules="[{ required: true, trigger: 'blur', message: '请选择设备类型' }]">
                                <a-select ref="select" :disabled="true" v-model:value="form.deviceType" placeholder="请选择" :options="deviceList" :fieldNames="{ label: 'name', value: 'type' }"></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="设备名称：" name="name" :rules="[{ required: true, trigger: 'blur', message: '请输入设备名称' }]">
                                <a-input v-model:value.trim="form.name" placeholder="请输入" show-count :maxlength="50" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="设备IMEI：" name="imei" :rules="[{ required: true, trigger: 'blur', message: '请输入设备IMEI' }]">
                                <a-input v-model:value.trim="form.imei" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="备注说明：" name="remark">
                                <a-textarea v-model:value.trim="form.remark" placeholder="请输入" :auto-size="{ minRows: 10, maxRows: 10 }" show-count :maxlength="100" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YDrawer>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const emit = defineEmits(["submitDrawer"])
const drawerOpen = ref(false)
const submitLoading = ref(false)

const formRef = ref(null)
const form = ref({})
const deviceList = ref([])
function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        http.post("/unicard/mgmt/device-info/create", form.value)
            .then((res) => {
                message.success(res.message)
                cancel()
                emit("submitDrawer")
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}

async function open(device, type) {
    deviceList.value = device || []
    setTimeout(() => {
        drawerOpen.value = true
        form.value = {}
        form.value.deviceType = type
    }, 100)
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
}
</style>
