<template>
    <div>
        <YModal v-model:open="openModal" title="修改设备管理员密码" @close="cancel" @cancel="cancel" :width="600">
            <div class="content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item label="旧密码：" name="oldPassword" :rules="[{ required: true, trigger: 'blur', message: '请输入旧密码' }]">
                                <a-input v-model:value.trim="form.oldPassword" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="新密码：" name="newPassword" :rules="[{ required: true, trigger: 'blur', message: '请输入新密码' }]">
                                <a-input v-model:value.trim="form.newPassword" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="确认新密码：" name="confirmPassword" :rules="[{ required: true, trigger: 'blur', message: '请再次输入新密码' }]">
                                <a-input v-model:value.trim="form.confirmPassword" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button key="back" @click="cancel">取消</a-button>
                <a-button key="submit" type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YModal>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const emit = defineEmits(["submitDrawer"])
const openModal = ref(false)
const submitLoading = ref(false)

const props = defineProps({
    deviceId: {
        type: String,
        default: ""
    }
})

const deviceId = computed(() => props.deviceId)
const formRef = ref(null)
const form = ref({})

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        http.post("/unicard/mgmt/device-info/update-password", { ...form.value, deviceId: deviceId.value })
            .then((res) => {
                message.success(res.message)
                cancel()
                emit("submitDrawer")
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}

function open() {
    openModal.value = true
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
}
</style>
