<template>
    <div>
        <YModal
            v-model:open="openModal"
            title="更新记录"
            @close="cancel"
            @cancel="cancel"
            :footer="null"
            :width="800"
        >
            <div class="content">
                <ETable
                    :columns="columns"
                    :data-source="dataSource"
                    :paginations="pagination"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{
                            index + 1
                        }}</template>
                        <template v-if="column.dataIndex == 'majorName'">{{
                            record.majorName
                        }}</template>
                    </template>
                </ETable>
            </div>
        </YModal>
    </div>
</template>

<script setup>
const openModal = ref(false)

const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = computed(() => {
    return [
        { title: "序号", dataIndex: "index", width: 100 },
        { title: "用户", dataIndex: "majorName", key: "majorName", width: 100 },
        {
            title: "更新内容",
            dataIndex: "majorName",
            key: "majorName",
            width: 100
        },
        {
            title: "更新时间",
            dataIndex: "majorName",
            key: "majorName",
            width: 100
        }
    ]
})

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

function open() {
    openModal.value = true
    getInitList()
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
}
</style>
