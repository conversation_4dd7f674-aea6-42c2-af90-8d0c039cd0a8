<template>
    <div>
        <YModal
            v-model:open="openModal"
            :title="modelTitle"
            @close="cancel"
            @cancel="cancel"
            :width="800"
        >
            <div class="content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]" v-if="statusType === 'bind'">
                        <a-col :span="24">
                            <a-form-item
                                label="选择商户："
                                name="merchantId"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请选择商户'
                                    }
                                ]"
                            >
                                <a-select
                                    ref="select"
                                    v-model:value="form.merchantId"
                                    :filter-option="filterOption"
                                    placeholder="请选择"
                                    show-search
                                    :options="merchantList"
                                    :fieldNames="{
                                        label: 'merchantName',
                                        value: 'id'
                                    }"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item
                                label="消费场景："
                                name="consumptionTypeId"
                                :rules="[
                                    {
                                        required: true,
                                        trigger: 'blur',
                                        message: '请消费场景'
                                    }
                                ]"
                            >
                                <a-select
                                    ref="select"
                                    v-model:value="form.consumptionTypeId"
                                    placeholder="请选择"
                                    :options="consumptionTypeList"
                                    :fieldNames="{
                                        label: 'typeName',
                                        value: 'id'
                                    }"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="备注说明：" name="majorName">
                                <a-textarea
                                    v-model:value.trim="form.majorName"
                                    placeholder="请输入"
                                    :auto-size="{ minRows: 10, maxRows: 10 }"
                                    show-count
                                    :maxlength="500"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                    <a-row :gutter="[24, 18]" v-if="statusType === 'unBind'">
                        <a-col :span="24">
                            <div class="unbind_tip">确定解绑商户？</div>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="备注说明：" name="majorName">
                                <a-textarea
                                    v-model:value.trim="form.majorName"
                                    placeholder="请输入"
                                    :auto-size="{ minRows: 10, maxRows: 10 }"
                                    show-count
                                    :maxlength="500"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button key="back" @click="cancel">取消</a-button>
                <a-button
                    key="submit"
                    type="primary"
                    :loading="submitLoading"
                    @click="submit"
                    >确定</a-button
                >
            </template>
        </YModal>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"

const emit = defineEmits(["submitDrawer"])
const openModal = ref(false)
const submitLoading = ref(false)

const formRef = ref(null)
const form = ref({})

const statusType = ref("bind")
const consumptionTypeList = ref([])
const deviceIds = ref([])
const merchantList = ref([])

const modelTitle = computed(() => {
    return statusType.value === "bind" ? "绑定商户" : "解绑商户"
})

// 下拉搜索
const filterOption = (input, option) => {
    return option.merchantName.indexOf(input) >= 0
}

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        const url =
            statusType.value === "bind"
                ? "/unicard/mgmt/device-info/bind-merchant"
                : "/unicard/mgmt/device-info/unbind-merchant"
        http.post(url, { ...form.value, ids: deviceIds.value })
            .then((res) => {
                message.success(res.message)
                cancel()
                emit("submitDrawer")
            })
            .finally(() => {
                submitLoading.value = false
            })
    })
}

// 商户列表
function getMerchantList() {
    // merchantTypes: [2, 3, 4] 代表获取类型为‘二级商户’，‘个人商户’，‘门店商户’的列表，在这里不需要‘一级商户’
    http.post("/unicard/mgmt/merchant-manage/list", {
        merchantTypes: [2, 3, 4]
    }).then((res) => {
        merchantList.value = res.data
    })
}

// 获取场景
function getConsumptionTypeList() {
    http.post("/unicard/mgmt/consumption-type/list").then((res) => {
        consumptionTypeList.value = res.data
    })
}

function open(type, device) {
    openModal.value = true
    form.value = {}
    statusType.value = type
    deviceIds.value = device
    getConsumptionTypeList()
    getMerchantList()
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
    .unbind_tip {
        width: 100%;
        font-weight: 400;
        font-size: 24px;
        color: #262626;
        line-height: 33px;
        text-align: center;
        margin: 40px 0;
    }
}
</style>
