<template>
    <div>
        <div class="device_manage">
            <div class="content">
                <search-form v-model:formState="query" :formList="formList" @submit="getInitList" layout="horizontal" @reset="reset" />
                <div class="btn_group">
                    <a-button type="primary" @click="addDeviceFn">
                        <template #icon>
                            <PlusOutlined />
                        </template>
                        添加设备
                    </a-button>
                    <a-button :disabled="!selectedRowKeys.length" @click="delectDevice('batch')">批量删除</a-button>
                    <a-button :disabled="!selectedRowKeys.length" @click="editBindStatusFn('bind')">绑定商户</a-button>
                    <a-button :disabled="!selectedRowKeys.length" @click="editBindStatusFn('unBind')">解绑商户</a-button>
                    <a-button :disabled="!selectedRowKeys.length" @click="editDeviceFn('batchEdit')">批量设备配置</a-button>
                    <a-button @click="pollCodeFn">设备注册码</a-button>
                </div>
                <ETable
                    :columns="columns"
                    :scroll="{ x: 1800 }"
                    :data-source="dataSource"
                    :paginations="pagination"
                    :loading="tableLoading"
                    @change="handleTableChange"
                    :row-selection="{
                        selectedRowKeys: selectedRowKeys,
                        onChange: onSelectedChange
                    }"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
                        <template v-else-if="column.dataIndex === 'deviceType'">
                            <span> {{ { payment: "消费机", cashier: "收银机" }[record.deviceType] }}</span>
                        </template>
                        <template v-else-if="column.dataIndex === 'onlineStatus'">
                            <span> {{ ["离线", "在线"][record.onlineStatus] }}</span>
                        </template>
                        <template v-else-if="column.dataIndex === 'bindingStatus'">
                            <span> {{ ["未绑定", "已绑定"][record.bindingStatus] }}</span>
                        </template>
                        <template v-else-if="column.dataIndex === 'operate'">
                            <a-button type="link" class="btn-link-color" @click="editDeviceFn('info', record)">详情</a-button>
                            <a-button type="link" class="btn-link-color" @click="editDeviceFn('edit', record)">编辑</a-button>
                            <a-button type="link" style="padding: 0" danger ghost @click="delectDevice('single', record)">删除设备</a-button>
                        </template>
                    </template>
                </ETable>
            </div>
            <!-- 绑定/解绑商户 -->
            <edit-bind-status ref="editBindStatusRef" @submitDrawer="reset" />

            <!-- 新增设备 -->
            <add-device ref="addDeviceRef" @submitDrawer="reset" />

            <!-- 设备注册码 -->
            <YModal v-model:open="openModal" title="设备注册码" @cancel="openModal = false" :footer="null" :width="500">
                <div class="registration_code_content">
                    {{ registrationCode || "暂无注册码" }}
                </div>
            </YModal>
        </div>
    </div>
</template>

<script setup>
import { message, Modal } from "ant-design-vue"
import { computed, createVNode } from "vue"
import { ExclamationCircleFilled } from "@ant-design/icons-vue"
import { useRouter } from "vue-router"

import AddDevice from "./addDevice.vue"
import EditBindStatus from "./editBindStatus.vue"

const props = defineProps({
    type: {
        type: String,
        default: "groupMealMachine"
    }
})

const openModal = ref(false)
const registrationCode = ref("")
const deviceList = ref([])
const pageType = computed(() => props.type)
const selectedRowKeys = ref([])
const router = useRouter()
const query = ref({})
const dataSource = ref([])
const tableLoading = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const formList = ref([
    {
        type: "input",
        value: "imei",
        label: "设备IMEI"
    },
    {
        type: "input",
        value: "name",
        label: "设备名称"
    },
    {
        type: "input",
        value: "merchantName",
        label: "关联商户"
    },
    {
        type: "select",
        value: "onlineStatus",
        label: "设备网络",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ]
    },
    {
        type: "select",
        value: "bindingStatus",
        label: "设备状态",
        list: [
            { label: "全部", value: null },
            { label: "已绑定", value: 1 },
            { label: "未绑定", value: 0 }
        ]
    },
    // {
    //     type: "select",
    //     value: "deviceType",
    //     label: "设备类型",
    //     list: deviceList.value,
    //     attrs: {
    //         fieldNames: {
    //             label: "name",
    //             value: "type"
    //         }
    //     }
    // },
    {
        type: "rangePicker",
        value: ["startLastOnlineDate", "endLastOnlineDate"],
        label: "最后在线时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])
const columns = ref([
    { title: "序号", dataIndex: "index", width: 60 },
    { title: "设备IMEI", dataIndex: "imei", key: "imei", width: 160 },
    { title: "设备类型", dataIndex: "deviceType", key: "deviceType" },
    { title: "设备型号", dataIndex: "deviceModel", key: "deviceModel" },
    { title: "关联商户", dataIndex: "merchantName", key: "merchantName" },
    { title: "设备名称", dataIndex: "name", key: "name" },
    { title: "设备网络", dataIndex: "onlineStatus", key: "onlineStatus" },
    { title: "APP版本信息", dataIndex: "appVersion", key: "appVersion", width: 360 },
    { title: "设备状态", dataIndex: "bindingStatus", key: "bindingStatus" },
    { title: "最后在线时间", dataIndex: "lastOnlineTime", key: "lastOnlineTime", width: 160 },
    { title: "操作", dataIndex: "operate", width: 180, fixed: "right" }
])

const editBindStatusRef = ref(null)
const addDeviceRef = ref(null)

// 设备注册码
function pollCodeFn() {
    http.get("/unicard/mgmt/device-info/get-registration-code").then((res) => {
        openModal.value = true
        registrationCode.value = res.data.registrationCode
    })
}
const deviceType = {
    groupMealMachine: "payment",
    cashRegister: "cashier"
}
// 新增设备
function addDeviceFn() {
    addDeviceRef.value.open(deviceList.value, deviceType[pageType.value])
}

// 绑定/解绑商户
function editBindStatusFn(type) {
    editBindStatusRef.value.open(type, selectedRowKeys.value)
}

function editDeviceFn(type, item) {
    if (type === "batchEdit") {
        const ids = selectedRowKeys.value.length ? JSON.stringify(selectedRowKeys.value) : null
        console.log(ids)
        router.push({
            path: `/deviceManage/${pageType.value}/edit`,
            query: {
                type,
                ids
            }
        })
    } else {
        router.push({
            path: `/deviceManage/${pageType.value}/edit`,
            query: {
                type,
                id: item.id || null
            }
        })
    }
}

// 删除设备
function delectDevice(type, item = {}) {
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "确定删除当前设备吗？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            const idsObj = {
                batch: selectedRowKeys.value, // 批量删除
                single: [item.id] // 当个删除
            }
            http.post("/unicard/mgmt/device-info/delete", { ids: idsObj[type] }).then((res) => {
                message.success(res.message)
                reset()
            })
        },
        onCancel() {
            message.info("已取消！")
        }
    })
}

function onSelectedChange(rowKeys) {
    selectedRowKeys.value = rowKeys
}
function getList() {
    tableLoading.value = true
    http.post("/unicard/mgmt/device-info/page", { ...pagination.value, ...query.value, deviceType: deviceType[pageType.value] })
        .then((res) => {
            dataSource.value = res.data?.list
            pagination.value.pageNo = res.data?.pageNo
            pagination.value.pageSize = res.data?.pageSize
            pagination.value.total = res.data?.total
        })
        .finally(() => {
            tableLoading.value = false
        })
}
function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

function reset() {
    selectedRowKeys.value = []
    query.value = {}
    getInitList()
}

function getDeviceList() {
    http.get("/unicard/mgmt/device-info/device-type").then((res) => {
        deviceList.value = res.data || []
    })
}

onMounted(() => {
    getDeviceList()
    reset()
})
</script>

<style lang="less" scoped>
.device_manage {
    .content {
        padding: 20px;
        .btn_group {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin: 16px 0;
        }
    }
}
.registration_code_content {
    padding: 50px;
    font-weight: 400;
    font-size: 48px;
    color: #262626;
    line-height: 67px;
    text-align: center;
}
</style>
