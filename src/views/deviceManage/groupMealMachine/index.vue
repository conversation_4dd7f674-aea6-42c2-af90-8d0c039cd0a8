<template>
    <div class="group_meal_machine_box">
        <page-index type="groupMealMachine" v-if="route.path === '/deviceManage/groupMealMachine'"></page-index>
        <router-view></router-view>
    </div>
</template>

<script setup>
import { useRoute } from "vue-router"
const route = useRoute()

import PageIndex from "../components/pageIndex.vue"
</script>

<style lang="less" scoped>
.group_meal_machine_box {
    max-height: calc(100vh - 174px);
}
</style>
