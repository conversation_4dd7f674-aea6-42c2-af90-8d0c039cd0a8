<template>
    <div class="edit_device">
        <div class="head">
            <div class="left">
                <ArrowLeftOutlined
                    @click="back"
                    :style="{ color: 'var(--primary-color)', fontSize: '16px' }"
                />
                <span class="page_title">{{
                    isEdit ? "编辑设备" : "设备详情"
                }}</span>
            </div>
            <a-button type="primary" @click="devicePassword" v-if="isEdit">
                设备超管密码
            </a-button>
        </div>
        <div class="content">
            <!-- 设备详情信息 -->
            <div class="device_info" v-if="!isEdit">
                <a-row :gutter="[24, 18]">
                    <a-col
                        :span="item.span"
                        v-for="item in infoLabel"
                        :key="item.key"
                    >
                        <div
                            v-if="item.key === 'splitLine'"
                            class="split_line"
                        ></div>
                        <div v-else-if="item.key === 'name'">
                            <div class="title_device">
                                {{ info[item.key] || "-" }}
                                <div class="device_status">
                                    {{
                                        info.onlineStatus === 1
                                            ? "在线"
                                            : "离线"
                                    }}
                                </div>
                            </div>
                            <div class="device_isbind">
                                {{
                                    info.bindingStatus === 1
                                        ? "已绑定"
                                        : "未绑定"
                                }}
                            </div>
                        </div>
                        <div v-else class="info">
                            <span
                                >{{ item.label }}：
                                <span v-if="item.key === 'deviceType'">{{
                                    { payment: "消费机", cashier: "收银机" }[
                                        info[item.key]
                                    ] || "-"
                                }}</span
                                ><span v-else>{{
                                    info[item.key] || "-"
                                }}</span></span
                            >
                            <span
                                v-if="
                                    item.key === 'updateTime' && info[item.key]
                                "
                                class="update_record"
                                @click="handleUpdateRecord"
                            >
                                更新记录<RightOutlined
                            /></span>
                        </div>
                    </a-col>
                </a-row>
            </div>
            <!-- 设备基础信息 -->
            <!-- 只有编辑有基础信息 -->
            <div v-if="isEdit && !isBatch">
                <div class="title_line">
                    <div class="line"></div>
                    设备基础信息：
                </div>
                <a-form
                    :disabled="!isEdit"
                    :model="basisInfoForm"
                    ref="basisInfoFormRef"
                    layout="vertical"
                    :rules="basisInfoRules"
                >
                    <a-row :gutter="[24, 18]">
                        <a-col :span="12">
                            <a-form-item label="设备类型：" name="deviceType">
                                <a-select
                                    ref="select"
                                    :disabled="true"
                                    v-model:value="basisInfoForm.deviceType"
                                    placeholder="请选择"
                                    :options="deviceList"
                                    :fieldNames="{
                                        label: 'name',
                                        value: 'type'
                                    }"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="设备网络：" name="onlineStatus">
                                <a-input
                                    :disabled="true"
                                    v-model:value.trim="
                                        ['离线', '在线'][
                                            basisInfoForm.onlineStatus
                                        ]
                                    "
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="设备IMEI：" name="imei">
                                <a-input
                                    :disabled="true"
                                    v-model:value.trim="basisInfoForm.imei"
                                    placeholder="请输入"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="设备名称：" name="name">
                                <a-input
                                    v-model:value.trim="basisInfoForm.name"
                                    placeholder="请输入"
                                    show-count
                                    :maxlength="50"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="备注说明：" name="remark">
                                <a-textarea
                                    v-model:value.trim="basisInfoForm.remark"
                                    placeholder="请输入"
                                    :auto-size="{ minRows: 2, maxRows: 10 }"
                                    show-count
                                    :maxlength="100"
                                />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <!-- 支付配置 -->
            <div class="form_box">
                <div class="title_line">
                    <div class="line"></div>
                    支付配置：
                </div>
                <a-form
                    :disabled="!isEdit"
                    :model="configForm"
                    ref="payConfigFormRef"
                    layout="horizontal"
                    :rules="payConfigRules"
                >
                    <a-row :gutter="[24, 18]">
                        <a-col
                            :span="
                                item.hide
                                    ? 0
                                    : [
                                          'payMode',
                                          'payType',
                                          'payMoneySpeedyObj'
                                      ].includes(item.key)
                                    ? 24
                                    : 12
                            "
                            v-for="item in payFormLabel"
                            :key="item.key"
                        >
                            <a-form-item
                                :label="item.label"
                                v-if="
                                    !item.hide &&
                                    item.key === 'payMoneySpeedyObj'
                                "
                            >
                                <a-row :gutter="[24, 18]">
                                    <a-col
                                        :span="6"
                                        v-for="sItem in item.list"
                                        :key="sItem.value"
                                    >
                                        <div class="speedy_item">
                                            <span>{{ sItem.label }}：</span>
                                            <a-input-number
                                                style="width: 100%; flex: 1"
                                                :step="1"
                                                :precision="2"
                                                v-model:value.trim="
                                                    payMoneySpeedyObj[
                                                        sItem.value
                                                    ]
                                                "
                                                placeholder="请输入"
                                            >
                                            </a-input-number>
                                        </div>
                                    </a-col>
                                </a-row>
                            </a-form-item>
                            <a-form-item
                                :label="item.label"
                                :name="item.key"
                                v-else-if="!item.hide"
                            >
                                <a-input-number
                                    v-if="item.key === 'payMoney'"
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="configForm[item.key]"
                                    placeholder="请输入"
                                >
                                </a-input-number>
                                <a-select
                                    v-else-if="item.key === 'payMode'"
                                    ref="select"
                                    v-model:value="configForm.payMode"
                                    placeholder="请选择"
                                    :options="item.list"
                                    :fieldNames="{
                                        label: 'name',
                                        value: 'type'
                                    }"
                                ></a-select>
                                <a-checkbox-group
                                    v-else-if="item.key === 'payType'"
                                    v-model:value="configForm.payType"
                                    name="checkboxgroup"
                                    :options="item.list"
                                />
                                <a-radio-group
                                    v-else
                                    v-model:value="configForm[item.key]"
                                    name="radioGroup"
                                    :options="item.list"
                                >
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <!-- 基础配置 -->
            <div class="form_box">
                <div class="title_line">
                    <div class="line"></div>
                    基础配置：
                </div>
                <a-form
                    :disabled="!isEdit"
                    :model="configForm"
                    ref="basisConfigFormRef"
                    layout="horizontal"
                    :rules="basisConfigRules"
                >
                    <a-row :gutter="[24, 18]">
                        <a-col :span="12">
                            <a-form-item
                                label="显示在主屏幕左上角文字："
                                name="titleText"
                            >
                                <a-input
                                    v-model:value.trim="configForm.titleText"
                                    placeholder="请输入"
                                    show-count
                                    :maxlength="50"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="显示在主屏幕右上角文字："
                                name="titleHint"
                            >
                                <a-input
                                    v-model:value.trim="configForm.titleHint"
                                    placeholder="请输入"
                                    show-count
                                    :maxlength="50"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="显示在主屏幕中间文字："
                                name="titleBack"
                            >
                                <a-input
                                    v-model:value.trim="configForm.titleBack"
                                    placeholder="请输入"
                                    show-count
                                    :maxlength="50"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="本次支付限额：" name="payLimit">
                                <a-input-number
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="configForm.payLimit"
                                    placeholder="请输入"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="姓名模式：" name="nameMode">
                                <a-radio-group
                                    v-model:value="configForm.nameMode"
                                    name="radioGroup"
                                    :options="[
                                        {
                                            label: '*姓',
                                            value: 1
                                        },
                                        {
                                            label: '*名',
                                            value: 2
                                        },
                                        {
                                            label: '*姓名',
                                            value: 0
                                        }
                                    ]"
                                >
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="是否展示余额："
                                name="spareVisibility"
                            >
                                <a-radio-group
                                    v-model:value="configForm.spareVisibility"
                                    name="radioGroup"
                                    :options="radioGroupOptions"
                                >
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="设备是否自动重启："
                                name="deviceRestart"
                            >
                                <a-radio-group
                                    v-model:value="configForm.deviceRestart"
                                    name="radioGroup"
                                    :options="radioGroupOptions"
                                >
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="自动重启时间："
                                name="deviceRestartTime"
                            >
                                <a-time-picker
                                    v-model:value="configForm.deviceRestartTime"
                                    format="HH:mm"
                                    valueFormat="HH:mm"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="支付成功倒计时："
                                name="countDownPaySucceed"
                            >
                                <a-input-number
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="
                                        configForm.countDownPaySucceed
                                    "
                                    placeholder="请输入"
                                    addon-after="秒"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="支付失败倒计时："
                                name="countDownPayFailure"
                            >
                                <a-input-number
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="
                                        configForm.countDownPayFailure
                                    "
                                    placeholder="请输入"
                                    addon-after="秒"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="RGB活体检测："
                                name="rgbFromAlive"
                            >
                                <a-radio-group
                                    v-model:value="configForm.rgbFromAlive"
                                    name="radioGroup"
                                    :options="radioGroupOptions"
                                >
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="RGB活体检测阀值："
                                name="rgbFromAliveRaft"
                            >
                                <a-input-number
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="
                                        configForm.rgbFromAliveRaft
                                    "
                                    placeholder="请输入"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="IR活体检测：" name="fromAlive">
                                <a-radio-group
                                    v-model:value="configForm.fromAlive"
                                    name="radioGroup"
                                    :options="radioGroupOptions"
                                >
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="IR活体检测阀值："
                                name="fromAliveRaft"
                            >
                                <a-input-number
                                    style="width: 100%"
                                    :step="1"
                                    :precision="2"
                                    v-model:value.trim="
                                        configForm.fromAliveRaft
                                    "
                                    placeholder="请输入"
                                >
                                </a-input-number>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <!-- 离线支付配置 -->
            <div class="form_box">
                <div class="title_line">
                    <div class="line"></div>
                    离线支付配置：
                </div>
                <a-form
                    :disabled="!isEdit"
                    :model="configForm"
                    ref="offlinePayFormRef"
                    layout="horizontal"
                >
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item label="模式配置：" name="payFlows">
                                <a-radio-group
                                    v-model:value="configForm.payFlows"
                                >
                                    <a-radio :value="0" :style="radioStyle">
                                        在线模式
                                        <span
                                            style="color: var(--warning-color)"
                                            >设备在交易过程中要等待服务器返回交易结果，如果设备断网则交易失败（支持所有的支付方式和消费模式）</span
                                        >
                                    </a-radio>
                                    <a-radio :value="2" :style="radioStyle">
                                        自动模式
                                        <span
                                            style="color: var(--warning-color)"
                                            >交易过程中自动检测联网状态，自动切换支付模式。若需要使用离线支付，设备需要绑定门店。</span
                                        >
                                    </a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </div>
        <div class="footer">
            <a-button @click="back">取消</a-button>
            <a-button type="primary" :loading="submitLoading" @click="submit"
                >确定</a-button
            >
        </div>

        <!-- 修改密码 -->
        <edit-password ref="editPasswordRef" :deviceId="route.query?.id" />

        <!-- 更新记录 -->
        <update-record ref="updateRecordRef" />
    </div>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router"
import EditPassword from "../../components/editPassword.vue"
import UpdateRecord from "../../components/updateRecord.vue"
import { message } from "ant-design-vue"
import { computed } from "vue"

const router = useRouter()
const route = useRoute()
const submitLoading = ref(false)

const deviceList = ref([])
const payModeList = ref([])

const radioGroupOptions = [
    {
        label: "是",
        value: true
    },
    {
        label: "否",
        value: false
    }
]

const info = ref({})

// 详情
const infoLabel = [
    {
        key: "name",
        label: "设备名称",
        span: 24
    },
    {
        key: "imei",
        label: "设备IMEI",
        span: 8
    },
    {
        key: "deviceType",
        label: "设备类型",
        span: 8
    },
    {
        key: "deviceModel",
        label: "设备型号",
        span: 8
    },
    {
        key: "lastOnlineTime",
        label: "最后在线时间",
        span: 8
    },
    {
        key: "splitLine",
        label: "分割线",
        span: 24
    },
    {
        key: "merchantName",
        label: "关联商户",
        span: 8
    },
    {
        key: "consumptionTypeName",
        label: "消费类型",
        span: 8
    },
    {
        key: "remark",
        label: "备注说明",
        span: 8
    },
    {
        key: "createBy",
        label: "创建人",
        span: 8
    },
    {
        key: "updateTime",
        label: "更新时间",
        span: 8
    }
]

// 编辑/详情
const isEdit = computed(() => ["edit", "batchEdit"].includes(route.query.type))
const isBatch = computed(() => route.query.type === "batchEdit")
const configForm = ref({})

// 基础信息
const basisInfoFormRef = ref(null)
const basisInfoForm = ref({})
const basisInfoRules = {
    name: [{ required: true, message: "请输入设备名称", trigger: "change" }]
}

// 支付配置
const payConfigFormRef = ref(null)
const payMoneySpeedyObj = ref({})
const payFormLabel = computed(() => {
    return [
        {
            label: "消费模式（单选）：",
            key: "payMode",
            list: payModeList.value
        },
        {
            label: "快捷⾦额配置：",
            key: "payMoneySpeedyObj",
            list: [
                { label: "按键0", value: "key_0" },
                { label: "按键1", value: "key_1" },
                { label: "按键2", value: "key_2" },
                { label: "按键3", value: "key_3" },
                { label: "按键4", value: "key_4" },
                { label: "按键5", value: "key_5" },
                { label: "按键6", value: "key_6" },
                { label: "按键7", value: "key_7" },
                { label: "按键8", value: "key_8" },
                { label: "按键9", value: "key_9" }
            ],
            hide: configForm.value.payMode !== 10 // 只有快捷模式才有
        },
        {
            label: "定额金额：",
            key: "payMoney",
            hide: configForm.value.payMode !== 2 // 只有定额模式才有
        },
        {
            label: "支付方式（多选）：",
            key: "payType",
            list: [
                { label: "刷脸⽀付⽅式", value: "payTypeFace" },
                { label: "刷卡⽀付⽅式", value: "payTypeCard" },
                { label: "扫码⽀付⽅式", value: "payTypeCode" }
            ]
        },
        {
            label: "自由模式展示开关：",
            key: "payModeCustom",
            list: radioGroupOptions
        },
        {
            label: "定额模式展示开关：",
            key: "payModeRation",
            list: radioGroupOptions
        },
        {
            label: "核销模式展示开关：",
            key: "payModeVerify",
            list: radioGroupOptions
        },
        {
            label: "时段定额展示开关：",
            key: "payModeTimely",
            list: radioGroupOptions
        },
        {
            label: "记账模式展示开关：",
            key: "payModeCredit",
            list: radioGroupOptions
        },
        {
            label: "分组定额模式展示开关：",
            key: "payModeGroups",
            list: radioGroupOptions
        },
        {
            label: "自由模式自助开始：",
            key: "payStartSwitch",
            list: radioGroupOptions
        },
        {
            label: "快捷收银模式展示开关：",
            key: "payModeSpeedy",
            list: radioGroupOptions
        },
        {
            label: "刷脸模式自动确认：",
            key: "faceAffirm",
            list: radioGroupOptions
        },
        {
            label: "识盘模式展示开关：",
            key: "payModePlates",
            list: radioGroupOptions
        },
        {
            label: "刷卡模式自动确认：",
            key: "cardAffirm",
            list: radioGroupOptions
        },
        {
            label: "扫码模式自动确认：",
            key: "codeAffirm",
            list: radioGroupOptions
        },
        {
            label: "取消按钮是否可点：",
            key: "cancelClicked",
            list: radioGroupOptions
        },
        {
            label: "小票打印开关：",
            key: "printSwitch",
            list: radioGroupOptions
        }
    ]
})
const payConfigRules = {
    payMode: [{ required: true, message: "请选择消费模式", trigger: "change" }],
    payType: [{ required: true, message: "请选择支付方式", trigger: "change" }],
    payMoney: [
        { required: true, message: "请输入定额金额", trigger: "change" }
    ],
    payModeCustom: [
        { required: true, message: "请选择自由模式展示开关", trigger: "change" }
    ],
    payModeRation: [
        { required: true, message: "请选择定额模式展示开关", trigger: "change" }
    ],
    payModeVerify: [
        { required: true, message: "请选择核销模式展示开关", trigger: "change" }
    ],
    payModeTimely: [
        { required: true, message: "请选择时段定额展示开关", trigger: "change" }
    ],
    payModeCredit: [
        { required: true, message: "请选择记账模式展示开关", trigger: "change" }
    ],
    payModeGroups: [
        {
            required: true,
            message: "请选择分组定额模式展示开关",
            trigger: "change"
        }
    ],
    payStartSwitch: [
        { required: true, message: "请选择自由模式自助开始", trigger: "change" }
    ],
    payModeSpeedy: [
        {
            required: true,
            message: "请选择快捷收银模式展示开关",
            trigger: "change"
        }
    ],
    faceAffirm: [
        { required: true, message: "请选择刷脸模式自动确认", trigger: "change" }
    ],
    payModePlates: [
        { required: true, message: "请选择识盘模式展示开关", trigger: "change" }
    ],
    cardAffirm: [
        { required: true, message: "请选择刷卡模式自动确认", trigger: "change" }
    ],
    codeAffirm: [
        { required: true, message: "请选择扫码模式自动确认", trigger: "change" }
    ],
    cancelClicked: [
        { required: true, message: "请选择取消按钮是否可点", trigger: "change" }
    ],
    printSwitch: [
        { required: true, message: "请选择小票打印开关", trigger: "change" }
    ]
}

// 基础配置
const basisConfigFormRef = ref(null)
const basisConfigRules = {
    payLimit: [
        { required: true, message: "请输入支付限额", trigger: "change" }
    ],
    countDownPaySucceed: [
        { required: true, message: "请输入支付成功倒计时", trigger: "change" }
    ],
    countDownPayFailure: [
        { required: true, message: "请输入支付失败倒计时", trigger: "change" }
    ]
}

// 离线支付配置
const offlinePayFormRef = ref(null)
const radioStyle = reactive({
    display: "flex",
    height: "30px",
    lineHeight: "30px"
})

const editPasswordRef = ref(null)
const updateRecordRef = ref(null)

// 设备超管密码
function devicePassword() {
    editPasswordRef.value.open()
}

// 更新记录
function handleUpdateRecord() {
    updateRecordRef.value.open()
}

function back() {
    router.back()
}

// 确认接口
function formSubmit() {
    payConfigFormRef.value.validate().then(() => {
        basisConfigFormRef.value.validate().then(() => {
            configForm.value.payMoneySpeedy =
                payMoneySpeedyObj.value &&
                JSON.stringify(payMoneySpeedyObj.value)
            configForm.value.payTypeFace = configForm.value.payType.includes(
                "payTypeFace"
            )
                ? 1
                : 0
            configForm.value.payTypeCode = configForm.value.payType.includes(
                "payTypeCode"
            )
                ? 1
                : 0
            configForm.value.payTypeCard = configForm.value.payType.includes(
                "payTypeCard"
            )
                ? 1
                : 0
            const obj = {
                ...basisInfoForm.value,
                config: {
                    ...configForm.value
                }
            }
            submitLoading.value = true
            const url = isBatch.value
                ? "/unicard/mgmt/device-payment/batch/update"
                : "/unicard/mgmt/device-payment/update"
            const param = isBatch.value
                ? {
                      ...obj,
                      ids: route.query.ids ? JSON.parse(route.query.ids) : []
                  }
                : obj
            http.post(url, param)
                .then((res) => {
                    message.success(res.message)
                    back()
                })
                .finally(() => {
                    submitLoading.value = true
                })
        })
    })
}

function submit() {
    if (isBatch.value) {
        formSubmit()
    } else {
        basisInfoFormRef.value.validate().then(() => {
            formSubmit()
        })
    }
}

function getInfo(id) {
    http.post("/unicard/mgmt/device-payment/get", { id }).then((res) => {
        info.value = res.data // 查看详情
        basisInfoForm.value = res.data // 基础信息
        configForm.value = res.data?.config || {} // 支付配置
        payMoneySpeedyObj.value =
            res.data?.payMoneySpeedy && JSON.parse(res.data.payMoneySpeedy)
        configForm.value.payType = []
        if (res.data.config.payTypeFace === 1) {
            configForm.value.payType.push("payTypeFace")
        }
        if (res.data.config.payTypeCode === 1) {
            configForm.value.payType.push("payTypeCode")
        }
        if (res.data.config.payTypeCard === 1) {
            configForm.value.payType.push("payTypeCard")
        }
    })
}

async function getDeviceList() {
    await http.get("/unicard/mgmt/device-info/device-type").then((res) => {
        deviceList.value = res.data || []
    })
}

async function getPayModeList() {
    await http.get("/unicard/mgmt/device-info/pay-mode").then((res) => {
        payModeList.value = res.data || []
    })
}

onMounted(async () => {
    await getDeviceList()
    await getPayModeList()
    if (route.query.id) {
        getInfo(route.query?.id)
    }
})
</script>

<style lang="less" scoped>
.edit_device {
    max-height: calc(100vh - 130px);
    overflow: hidden;
    position: relative;
    top: -58px;
    background: var(--bg-color);
    .head {
        padding: 18px 20px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
            display: flex;
            align-items: center;
        }
        .page_title {
            margin-left: 10px;
            font-weight: 600;
            font-size: 18px;
            color: var(--text-color);
            line-height: 25px;
        }
    }
    .content {
        max-width: 1200px;
        margin: auto;
        scrollbar-width: none;
        scrollbar-color: transparent transparent;
        max-height: calc(100vh - 182px);
        overflow: auto;
        padding: 20px 20px 120px 20px;

        .form_box {
            margin-bottom: 20px;
        }

        .title_line {
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            color: var(--text-color);
            line-height: 20px;
            margin-bottom: 12px;
            .line {
                margin-right: 4px;
                width: 2px;
                height: 12px;
                background: var(--primary-color);
            }
        }
    }
    .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        background: var(--bg-color);
        width: 100%;
        padding-top: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-top: 1px solid #d8d8d8;
    }
    .device_info {
        min-height: 100px;
        background: #f6f6f6;
        margin-bottom: 20px;
        padding: 16px;
        border-radius: 4px;
        position: relative;
        .split_line {
            height: 1px;
            background: #e6e6e6;
            width: 100%;
        }
        .update_record {
            color: var(--primary-color);
            cursor: pointer;
            line-height: 22px;
            padding-left: 10px;
        }
        .title_device {
            font-weight: 500;
            font-size: 18px;
            color: var(--text-color);
            line-height: 22px;
            display: flex;
            align-items: center;
            .device_status {
                background: var(--primary-color-bg);
                border-radius: 4px;
                border: 1px solid var(--primary-color);
                padding: 4px;
                font-weight: 400;
                font-size: 10px;
                color: var(--primary-color);
                line-height: 12px;
                margin-left: 6px;
            }
        }
        .device_isbind {
            position: absolute;
            top: -16px;
            right: -4px;
            width: 55px;
            height: 24px;
            background: var(--primary-color);
            border-radius: 0px 4px 0px 13px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ffffff;
        }
        .info {
            color: var(--text-color);
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
        }
    }
    .speedy_item {
        display: flex;
        align-items: center;
    }
}
</style>
