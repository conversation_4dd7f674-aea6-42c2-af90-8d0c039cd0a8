<template>
    <div class="edit_device">
        <div class="head">
            <div class="left">
                <ArrowLeftOutlined @click="back" :style="{ color: 'var(--primary-color)', fontSize: '16px' }" />
                <span class="page_title">{{ isEdit ? "编辑设备" : "设备详情" }}</span>
            </div>
            <a-button type="primary" @click="devicePassword" v-if="isEdit"> 设备超管密码 </a-button>
        </div>
        <div class="content">
            <!-- 设备详情信息 -->
            <div class="device_info" v-if="!isEdit">
                <a-row :gutter="[24, 18]">
                    <a-col :span="item.span" v-for="item in infoLabel" :key="item.key">
                        <div v-if="item.key === 'splitLine'" class="split_line"></div>
                        <div v-else-if="item.key === 'name'">
                            <div class="title_device">
                                {{ info[item.key] || "-" }}
                                <div class="device_status">{{ info.onlineStatus === 1 ? "在线" : "离线" }}</div>
                            </div>
                            <div class="device_isbind">{{ info.bindingStatus === 1 ? "已绑定" : "未绑定" }}</div>
                        </div>
                        <div v-else class="info">
                            <span
                                >{{ item.label }}： <span v-if="item.key == 'deviceType'">{{ { payment: "消费机", cashier: "收银机" }[info[item.key]] || "-" }}</span
                                ><span v-else>{{ info[item.key] || "-" }}</span></span
                            >
                            <span v-if="item.key === 'updateTime' && info[item.key]" class="update_record" @click="handleUpdateRecord"> 更新记录<RightOutlined /></span>
                        </div>
                    </a-col>
                </a-row>
            </div>
            <!-- 设备基础信息 -->
            <!-- 只有编辑有基础信息 -->
            <div v-if="isEdit && !isBatch">
                <div class="title_line">
                    <div class="line"></div>
                    设备基础信息：
                </div>
                <a-form :disabled="!isEdit" :model="basisInfoForm" ref="basisInfoFormRef" layout="vertical" :rules="basisInfoRules">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="12">
                            <a-form-item label="设备类型：" name="deviceType">
                                <a-select ref="select" :disabled="true" v-model:value="basisInfoForm.deviceType" placeholder="请选择" :options="deviceList" :fieldNames="{ label: 'name', value: 'type' }"></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="设备网络：" name="onlineStatus">
                                <a-input :disabled="true" v-model:value.trim="['离线', '在线'][basisInfoForm.onlineStatus]" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="设备IMEI：" name="imei">
                                <a-input :disabled="true" v-model:value.trim="basisInfoForm.imei" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="设备名称：" name="name">
                                <a-input v-model:value.trim="basisInfoForm.name" placeholder="请输入" show-count :maxlength="50" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="备注说明：" name="remark">
                                <a-textarea v-model:value.trim="basisInfoForm.remark" placeholder="请输入" :auto-size="{ minRows: 2, maxRows: 10 }" show-count :maxlength="100" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <!-- 支付配置 -->
            <div class="form_box">
                <div class="title_line">
                    <div class="line"></div>
                    支付配置：
                </div>
                <a-form :disabled="!isEdit" :model="configForm" ref="payConfigFormRef" layout="horizontal" :rules="payConfigRules">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24" v-for="item in payFormLabel" :key="item.key">
                            <a-form-item :label="item.label" :name="item.key">
                                <a-select :disabled="true" v-if="item.key === 'payMode'" ref="select" v-model:value="configForm.payMode" placeholder="请选择" :options="item.list" :fieldNames="{ label: 'name', value: 'type' }"></a-select>
                                <a-checkbox-group :disabled="true" v-else-if="item.key === 'payType'" v-model:value="configForm.payType" name="checkboxgroup" :options="item.list" />
                                <a-input v-else-if="item.key === 'receiptBottomText'" v-model:value.trim="configForm.receiptBottomText" placeholder="请输入" />
                                <a-input-number v-else-if="item.key == 'faceSimilarityThreshold'" style="width: 100%" :step="1" :precision="2" v-model:value.trim="configForm.faceSimilarityThreshold" placeholder="请输入"> </a-input-number>
                                <a-radio-group v-else v-model:value="configForm[item.key]" name="radioGroup" :options="item.list"> </a-radio-group>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </div>
        <div class="footer">
            <a-button @click="back">取消</a-button>
            <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
        </div>

        <!-- 修改密码 -->
        <edit-password ref="editPasswordRef" :deviceId="route.query?.id" />

        <!-- 更新记录 -->
        <update-record ref="updateRecordRef" />
    </div>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router"
import EditPassword from "../../components/editPassword.vue"
import UpdateRecord from "../../components/updateRecord.vue"
import { message } from "ant-design-vue"

const router = useRouter()
const route = useRoute()
const submitLoading = ref(false)

const deviceList = ref([])
// 前端暂且写死
const payModeList = ref([
    {
        name: "收银模式",
        type: 0
    }
])

const radioGroupOptions = [
    {
        label: "是",
        value: true
    },
    {
        label: "否",
        value: false
    }
]

const info = ref({})

// 详情
const infoLabel = [
    {
        key: "name",
        label: "设备名称",
        span: 24
    },
    {
        key: "imei",
        label: "设备IMEI",
        span: 8
    },
    {
        key: "deviceType",
        label: "设备类型",
        span: 8
    },
    {
        key: "deviceModel",
        label: "设备型号",
        span: 8
    },
    {
        key: "lastOnlineTime",
        label: "最后在线时间",
        span: 8
    },
    {
        key: "splitLine",
        label: "分割线",
        span: 24
    },
    {
        key: "merchantName",
        label: "关联商户",
        span: 8
    },
    {
        key: "consumptionTypeName",
        label: "消费类型",
        span: 8
    },
    {
        key: "remark",
        label: "备注说明",
        span: 8
    },
    {
        key: "createBy",
        label: "创建人",
        span: 8
    },
    {
        key: "updateTime",
        label: "更新时间",
        span: 8
    }
]

// 编辑/详情
const isEdit = computed(() => ["edit", "batchEdit"].includes(route.query.type))
const isBatch = computed(() => route.query.type === "batchEdit")
const configForm = ref({})

// 基础信息
const basisInfoFormRef = ref(null)
const basisInfoForm = ref({})
const basisInfoRules = {
    name: [{ required: true, message: "请输入设备名称", trigger: "change" }]
}

// 支付配置
const payConfigFormRef = ref(null)
const payMoneySpeedyObj = ref({})
const payFormLabel = computed(() => {
    return [
        {
            label: "消费模式（单选）：",
            key: "payMode",
            list: payModeList.value
        },
        {
            label: "支付方式（多选）：",
            key: "payType",
            list: [
                { label: "刷脸⽀付⽅式", value: "payTypeFace" },
                { label: "刷卡⽀付⽅式", value: "payTypeCard" },
                { label: "扫码⽀付⽅式", value: "payTypeCode" }
            ]
        },
        {
            label: "支付语音播报开关：",
            key: "succeededPaymentSpeechEnabled",
            list: radioGroupOptions
        },
        {
            label: "取消支付语音播报开关：",
            key: "cancelledPaymentSpeechEnabled",
            list: radioGroupOptions
        },
        {
            label: "商品扫码提示音开关：",
            key: "itemScannedSpeechEnabled",
            list: radioGroupOptions
        },
        {
            label: "用户扫码提示音开关：",
            key: "userScannedSpeechEnabled",
            list: radioGroupOptions
        },
        {
            label: "小票打印开关：",
            key: "receiptPrintingEnabled",
            list: radioGroupOptions
        },
        {
            label: "小票底部提示语：",
            key: "receiptBottomText",
            list: radioGroupOptions
        },
        {
            label: "设置页面是否展示用户列表：",
            key: "showUsersInSetting",
            list: radioGroupOptions
        },
        {
            label: "设置页面是否展示特征值列表：",
            key: "showFacesInSetting",
            list: radioGroupOptions
        },
        {
            label: "人脸比对阈值：",
            key: "faceSimilarityThreshold",
            list: radioGroupOptions
        }
    ]
})
const payConfigRules = {
    payMode: [{ required: true, message: "请选择消费模式", trigger: "change" }],
    payType: [{ required: true, message: "请选择支付方式", trigger: "change" }],
    succeededPaymentSpeechEnabled: [{ required: true, message: "请选择支付语音播报开关", trigger: "change" }],
    cancelledPaymentSpeechEnabled: [{ required: true, message: "请选择取消支付语音播报开关", trigger: "change" }],
    itemScannedSpeechEnabled: [{ required: true, message: "请选择商品扫码提示音开关", trigger: "change" }],
    userScannedSpeechEnabled: [{ required: true, message: "请选择用户扫码提示音开关", trigger: "change" }],
    receiptPrintingEnabled: [{ required: true, message: "请选择小票打印开关", trigger: "change" }],
    receiptBottomText: [{ required: true, message: "请输入小票底部提示语", trigger: "change" }],
    showUsersInSetting: [{ required: true, message: "请设置页面是否展示用户列表", trigger: "change" }],
    showFacesInSetting: [{ required: true, message: "请设置页面是否展示特征值列表", trigger: "change" }],
    faceSimilarityThreshold: [{ required: true, message: "请输入人脸比对阈值", trigger: "change" }]
}

const editPasswordRef = ref(null)
const updateRecordRef = ref(null)

// 设备超管密码
function devicePassword() {
    editPasswordRef.value.open()
}

// 更新记录
function handleUpdateRecord() {
    updateRecordRef.value.open()
}

function back() {
    router.back()
}

function formSubmit() {
    payConfigFormRef.value.validate().then(() => {
        configForm.value.payMoneySpeedy = payMoneySpeedyObj.value && JSON.stringify(payMoneySpeedyObj.value)
        configForm.value.payTypeFace = configForm.value.payType.includes("payTypeFace") ? 1 : 0
        configForm.value.payTypeCode = configForm.value.payType.includes("payTypeCode") ? 1 : 0
        configForm.value.payTypeCard = configForm.value.payType.includes("payTypeCard") ? 1 : 0
        const obj = {
            ...basisInfoForm.value,
            config: {
                ...configForm.value
            }
        }
        submitLoading.value = true
        const url = isBatch.value ? "/unicard/mgmt/device-cashier/batch/update" : "/unicard/mgmt/device-cashier/update"
        const param = isBatch.value ? { ...obj, ids: route.query.ids ? JSON.parse(route.query.ids) : [] } : obj
        http.post(url, param)
            .then((res) => {
                message.success(res.message)
                back()
            })
            .finally(() => {
                submitLoading.value = true
            })
    })
}

function submit() {
    if (isBatch.value) {
        formSubmit()
    } else {
        basisInfoFormRef.value.validate().then(() => {
            formSubmit()
        })
    }
}

function getInfo(id) {
    http.post("/unicard/mgmt/device-cashier/get", { id }).then((res) => {
        info.value = res.data // 查看详情
        basisInfoForm.value = res.data // 基础信息
        configForm.value = res.data?.config || {} // 支付配置
    })
}

async function getDeviceList() {
    await http.get("/unicard/mgmt/device-info/device-type").then((res) => {
        deviceList.value = res.data || []
    })
}

onMounted(async () => {
    await getDeviceList()
    if (route.query.id) {
        getInfo(route.query?.id)
    }
    configForm.value.payType = ["payTypeFace", "payTypeCode", "payTypeCard"] // 前端暂且写死
    configForm.value.payMode = 0 // 前端暂且写死
})
</script>

<style lang="less" scoped>
.edit_device {
    max-height: calc(100vh - 130px);
    overflow: hidden;
    position: relative;
    top: -58px;
    background: var(--bg-color);
    .head {
        padding: 18px 20px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
            display: flex;
            align-items: center;
        }
        .page_title {
            margin-left: 10px;
            font-weight: 600;
            font-size: 18px;
            color: var(--text-color);
            line-height: 25px;
        }
    }
    .content {
        max-width: 1200px;
        margin: auto;
        scrollbar-width: none;
        scrollbar-color: transparent transparent;
        max-height: calc(100vh - 182px);
        overflow: auto;
        padding: 20px 20px 120px 20px;

        .form_box {
            margin-bottom: 20px;
        }

        .title_line {
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 14px;
            color: var(--text-color);
            line-height: 20px;
            margin-bottom: 12px;
            .line {
                margin-right: 4px;
                width: 2px;
                height: 12px;
                background: var(--primary-color);
            }
        }
    }
    .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        background: var(--bg-color);
        width: 100%;
        padding-top: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-top: 1px solid #d8d8d8;
    }
    .device_info {
        min-height: 100px;
        background: #f6f6f6;
        margin-bottom: 20px;
        padding: 16px;
        border-radius: 4px;
        position: relative;
        .split_line {
            height: 1px;
            background: #e6e6e6;
            width: 100%;
        }
        .update_record {
            color: var(--primary-color);
            cursor: pointer;
            line-height: 22px;
            padding-left: 10px;
        }
        .title_device {
            font-weight: 500;
            font-size: 18px;
            color: var(--text-color);
            line-height: 22px;
            display: flex;
            align-items: center;
            .device_status {
                background: var(--primary-color-bg);
                border-radius: 4px;
                border: 1px solid var(--primary-color);
                padding: 4px;
                font-weight: 400;
                font-size: 10px;
                color: var(--primary-color);
                line-height: 12px;
                margin-left: 6px;
            }
        }
        .device_isbind {
            position: absolute;
            top: -16px;
            right: -4px;
            width: 55px;
            height: 24px;
            background: var(--primary-color);
            border-radius: 0px 4px 0px 13px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ffffff;
        }
        .info {
            color: var(--text-color);
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
        }
    }
    .speedy_item {
        display: flex;
        align-items: center;
    }
}
</style>
