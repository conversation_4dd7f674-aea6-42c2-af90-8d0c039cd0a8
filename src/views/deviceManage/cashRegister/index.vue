<template>
    <div class="cash_register_box">
        <page-index type="cashRegister" v-if="route.path === '/deviceManage/cashRegister'"></page-index>
        <router-view></router-view>
    </div>
</template>

<script setup>
import { useRoute } from "vue-router"
const route = useRoute()

import PageIndex from "../components/pageIndex.vue"
</script>

<style lang="less" scoped>
.cash_register_box {
    max-height: calc(100vh - 174px);
}
</style>
