<template>
    <div class="page_content">
        <div class="content_box">
            <!-- 头部 -->
            <div class="card_head">
                <span class="title">入库管理</span>
            </div>
            <div class="content_page">
                <!-- 搜索组件区域 -->
                <search-form
                    style="margin-bottom: 20px"
                    v-model:formState="query"
                    :formList="formList"
                    @submit="getInitList"
                    layout="horizontal"
                    @reset="reset"
                />

                <!-- 按钮区域 -->
                <div class="btn_group">
                    <a-button type="primary" @click="addDrawerFn('add', null)">
                        立即入库
                    </a-button>
                    <a-button
                        :disabled="!state.selectedRowKeys.length"
                        @click="editStockDrawerRef.open(state.selectTable)"
                        >批量修改库存</a-button
                    >
                    <a-button @click="commodityTypeDrawerRef.open()"
                        >商品类型管理</a-button
                    >

                    <a-button @click="isImport = true">批量入库</a-button>
                    <a-button
                        @click="deleteCommodity"
                        :disabled="!state.selectedRowKeys.length"
                        >批量删除</a-button
                    >
                    <a-button @click="updateRecordRef.open()"
                        >库存更新记录</a-button
                    >
                </div>
                <div class="table_box">
                    <!-- 表格 -->
                    <ETable
                        :columns="columns"
                        :data-source="state.dataSource"
                        :paginations="state.pagination"
                        @change="handleTableChange"
                        :loading="state.tableLoading"
                        :row-selection="{
                            selectedRowKeys: state.selectedRowKeys,
                            onChange: onSelectChange
                        }"
                    >
                        <template #bodyCell="{ column, record, index }">
                            <template v-if="column.dataIndex == 'operate'">
                                <a-button
                                    type="link"
                                    class="btn-link-color"
                                    @click="addDrawerFn('detail', record)"
                                    >详情</a-button
                                >
                                <a-button
                                    type="link"
                                    class="btn-link-color"
                                    @click="addDrawerFn('edit', record)"
                                    >编辑</a-button
                                >
                                <a-button
                                    type="link"
                                    danger
                                    class="btn-link-color"
                                    @click="deleteCommodityItem(record)"
                                    >删除</a-button
                                >
                            </template>
                        </template>
                    </ETable>
                </div>
            </div>
        </div>
    </div>
    <CommodityDrawer
        ref="commodityDrawerRef"
        @submitDrawer="handleSubmitDrawer"
    ></CommodityDrawer>
    <CommodityTypeDrawer ref="commodityTypeDrawerRef"></CommodityTypeDrawer>
    <EditStockDrawer ref="editStockDrawerRef"></EditStockDrawer>
    <UpdateRecord ref="updateRecordRef"></UpdateRecord>
    <YImport
        v-model:show="isImport"
        :importType="1"
        title="导入商品"
        @cancel="reset"
        templateSrc="/template/商品导入模版.xlsx"
        uploadUrl="/unicard/common/import"
        :progressRequest="progressRequest"
        :errorExport="errorExport"
    ></YImport>
</template>

<script setup>
import { createVNode, reactive } from "vue"
import { useRouter, useRoute } from "vue-router"
import YImport from "@/components/YImport/index.vue"
import CommodityDrawer from "./component/CommodityDrawer.vue"
import CommodityTypeDrawer from "./component/CommodityTypeDrawer.vue"
import EditStockDrawer from "./component/EditStockPageDrawer.vue"
import UpdateRecord from "./component/UpdateRecord.vue"
import { message, Modal } from "ant-design-vue"
import { ExclamationCircleFilled } from "@ant-design/icons-vue"
import http from "@/utils/http"

const isImport = ref(false)

const router = useRouter()
const route = useRoute()
const commodityDrawerRef = ref(null)
const commodityTypeDrawerRef = ref(null)
const editStockDrawerRef = ref(null)
const updateRecordRef = ref(null)
const query = ref({})

const state = reactive({
    tableLoading: false,
    selectedRowKeys: [],
    selectTable: [],
    dataSource: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
})

const columns = ref([
    {
        title: "序号",
        dataIndex: "index",
        width: 100,
        customRender: (row) => `${row.index + 1}`
    },
    { title: "商品条码", dataIndex: "barcode", key: "barcode" },
    {
        title: "商品名称",
        dataIndex: "productName",
        key: "productName"
    },
    { title: "价格（元）", dataIndex: "createBy", key: "createBy" },
    {
        title: "剩余库存",
        dataIndex: "createTime",
        key: "createTime"
    },
    {
        title: "商品类型",
        dataIndex: "createTime",
        key: "createTime"
    },
    {
        title: "商品状态",
        dataIndex: "status",
        key: "status"
    },
    {
        title: "更新时间",
        dataIndex: "createTime",
        key: "createTime"
    },
    { title: "操作", dataIndex: "operate", width: 150, fixed: "right" }
])

const formList = ref([
    {
        type: "input",
        value: "barcode",
        label: "商品条码"
    },
    {
        type: "input",
        value: "productName",
        label: "商品名称"
    },
    {
        type: "select",
        value: "typeId",
        label: "商品类型",
        list: [],
        attrs: {
            fieldNames: { label: "typeName", value: "id" }
        }
    },
    {
        type: "select",
        value: "status",
        label: "商品状态",
        list: [
            { name: "上架", value: "online" },
            { name: "下架", value: "offline" }
        ],
        attrs: {
            fieldNames: { label: "name", value: "value" }
        }
    }
])

const getList = () => {
    state.tableLoading = true
    http.post("/unicard/merchant-product/page", {
        ...state.pagination,
        ...query.value
    })
        .then((res) => {
            state.dataSource = res.data?.list
            state.pagination.pageNo = res.data?.pageNo
            state.pagination.pageSize = res.data?.pageSize
            state.pagination.total = res.data?.total
            state.tableLoading = false
        })
        .catch(() => {
            state.tableLoading = false
        })
}

function handleTableChange({ current, pageSize, total }) {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    getList()
}

const getInitList = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

function reset() {
    query.value = {}
    getInitList()
}

// 打开抽屉
const addDrawerFn = (type, record = {}) => {
    if (type === "add") {
        // 新建商品
        commodityDrawerRef.value?.open("add")
    } else if (type === "edit") {
        // 编辑商品
        commodityDrawerRef.value?.open("edit", record)
    } else if (type === "detail") {
        // 商品详情
        commodityDrawerRef.value?.open("detail", record)
    }
}

// 处理商品抽屉提交事件
const handleSubmitDrawer = () => {
    // 刷新商品列表
    getInitList()
}

// 获取商品分类的列表数据
const getTypeList = () => {
    http.post("/unicard/merchant-product-type/list", {}).then((res) => {
        state.typeList = res.data
        formList.value[2].list = res.data
        console.log(formList.value)
    })
}

const onSelectChange = (selectedRowKeys, data) => {
    state.selectedRowKeys = selectedRowKeys
    state.selectTable = data
}

const deleteCommodity = () => {
    Modal.confirm({
        title: "确认删除",
        icon: createVNode(ExclamationCircleFilled),
        content: "确认批量删除当前商品吗？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            http.post("/unicard/merchant-product/delete", {
                ids: state.selectedRowKeys
            }).then((res) => {
                state.selectedRowKeys = []
                message.success(res.message)
                getInitList()
            })
        },
        onCancel() {}
    })
}

const deleteCommodityItem = (record) => {
    Modal.confirm({
        title: "确认删除",
        icon: createVNode(ExclamationCircleFilled),
        content: "确认删除当前商品吗？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            http.post("/unicard/merchant-product/delete", {
                ids: [record.id]
            }).then((res) => {
                message.success(res.message)
                getInitList()
            })
        },
        onCancel() {}
    })
}

// 请求进度条数据
async function progressRequest(importId) {
    const { data } = await http.post("/unicard/common/import/progress", {
        importId
    })
    return data
}

// 异常数据下载
function errorExport(importIds) {
    const params = { importId: importIds[0], importType: 1 }
    http.download(
        "/unicard/common/export/importErrorLog",
        params,
        "导入商品异常数据.xlsx"
    )
}

onMounted(() => {
    reset()
    getTypeList()
})
</script>

<style lang="less" scoped>
.page_content {
    background: #ffffff;
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;
    .content_box {
        flex: 1;
        .card_head {
            width: 100%;
            padding: 16px 20px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-weight: 500;
                font-size: 18px;
                color: var(--text-color);
                line-height: 25px;
            }
        }
        .content_page {
            padding: 20px;
            width: 100%;
            .btn_group {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                margin-bottom: 16px;
            }
            .table_box {
                width: 100%;
            }
        }
    }
}
</style>
