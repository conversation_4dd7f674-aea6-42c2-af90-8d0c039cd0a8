<template>
    <YDrawer
        v-model:open="drawerOpen"
        title="库存修改记录"
        @close="cancel"
        :footerStyle="{ display: 'none' }"
    >
        <div class="drawer-content">
            <!-- 搜索组件区域 -->
            <search-form
                style="margin-bottom: 20px"
                v-model:formState="query"
                :formList="formList"
                @submit="queryList"
                layout="horizontal"
                @reset="resetList"
            />

            <div class="table_box">
                <!-- 表格 -->
                <ETable
                    :columns="columns"
                    :data-source="state.dataSource"
                    :paginations="state.pagination"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index }"> </template>
                </ETable>
            </div>
        </div>
    </YDrawer>
</template>

<script setup>
import { ref, computed, nextTick } from "vue"

import { PlusOutlined } from "@ant-design/icons-vue"

const query = ref({})

const state = {
    dataSource: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
}
const formList = ref([
    {
        type: "input",
        value: "barcode",
        label: "商品条码"
    },
    {
        type: "input",
        value: "productName",
        label: "商品名称"
    },
    {
        type: "select",
        value: "typeId",
        label: "商品类型",
        list: [],
        attrs: {
            fieldNames: { label: "typeName", value: "id" }
        }
    },
    {
        type: "input",
        value: "createBy",
        label: "操作人"
    },
    {
        type: "rangePicker",
        value: ["startDate", "endDate"],
        label: "操作时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const drawerOpen = ref(false)

const columns = ref([
    {
        title: "序号",
        dataIndex: "index",
        width: 100,
        customRender: (row) => `${row.index + 1}`
    },
    { title: "商品条码", dataIndex: "barcode", key: "barcode", width: 100 },
    {
        title: "商品名称",
        dataIndex: "productName",
        key: "productName",
        width: 100
    },
    { title: "价格（元）", dataIndex: "price", key: "price", width: 100 },
    {
        title: "商品类型",
        dataIndex: "typeName",
        key: "typeName",
        width: 100
    },
    {
        title: "当前库存",
        dataIndex: "stock",
        key: "stock",
        width: 100
    },
    {
        title: "添加库存",
        dataIndex: "createTime",
        key: "createTime",
        width: 100
    },
    {
        title: "最后库存",
        dataIndex: "createTime",
        key: "createTime",
        width: 100
    },
    {
        title: "操作人",
        dataIndex: "updateBy",
        key: "updateBy",
        width: 100
    },
    {
        title: "操作时间",
        dataIndex: "updateTime",
        key: "updateTime",
        width: 100
    }
])

// 打开抽屉
const open = () => {
    getList()
    getTypeList()
    drawerOpen.value = true
}

// 关闭抽屉
const cancel = () => {
    drawerOpen.value = false
}

const getList = () => {
    http.post("/unicard/merchant-product/pageStockLog", {
        ...state.pagination,
        ...query.value
    }).then((res) => {
        state.dataSource = res.data?.list
        state.pagination.pageNo = res.data?.pageNo
        state.pagination.pageSize = res.data?.pageSize
        state.pagination.total = res.data?.total
    })
}

// 获取商品分类的列表数据
const getTypeList = () => {
    http.post("/unicard/merchant-product-type/list", {}).then((res) => {
        formList.value[2].list = res.data
    })
}

const queryList = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

const resetList = () => {
    query.value = {}
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

const handleTableChange = (paginationObj, filters, sorter) => {
    state.pagination.pageNo = paginationObj.current
    state.pagination.pageSize = paginationObj.pageSize
    getList()
}

// 暴露给父组件的方法
defineExpose({
    open
})
</script>

<style lang="less" scoped>
.drawer-content {
    background-color: #ffffff;
}
</style>
