<template>
    <YDrawer
        v-model:open="drawerOpen"
        title="商品类型管理"
        @close="cancel"
        :footerStyle="{ display: 'none' }"
    >
        <div class="drawer-content">
            <div class="btn-group">
                <a-button type="primary" @click="addType">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建分类
                </a-button>
            </div>
            <div class="table_box">
                <!-- 表格 -->
                <ETable
                    :columns="columns"
                    :data-source="state.dataSource"
                    :paginations="pagination"
                    @change="handleTableChange"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'operate'">
                            <a-button
                                type="link"
                                class="btn-link-color"
                                @click="editType(record)"
                                >编辑</a-button
                            >
                            <a-button
                                danger
                                @click="delType(record)"
                                type="link"
                                class="btn-link-color"
                                >删除</a-button
                            >
                        </template>
                    </template>
                </ETable>
            </div>
        </div>
    </YDrawer>

    <a-modal
        v-model:open="state.open"
        :title="state.type == 'add' ? '新建分类' : '编辑分类'"
        width="428px"
        :body-style="{ padding: '24px' }"
        :keyboard="false"
        :maskClosable="false"
        cancelText="取消"
        okText="确认"
        :confirmLoading="state.loading"
        @ok="submitbtn"
        @cancel="cancelbtn"
    >
        <div>
            <a-form
                :model="state.typeState"
                ref="typeStateRef"
                layout="vertical"
            >
                <a-form-item
                    label="类型名称"
                    name="typeName"
                    :rules="[{ required: true, message: '请输入' }]"
                >
                    <a-input
                        placeholder="请输入"
                        show-count
                        :maxlength="8"
                        v-model:value="state.typeState.typeName"
                    />
                </a-form-item>
            </a-form>
        </div>
    </a-modal>
</template>

<script setup>
import { ref, computed, nextTick, createVNode } from "vue"

import { PlusOutlined, ExclamationCircleFilled } from "@ant-design/icons-vue"
import { Modal, message } from "ant-design-vue"

const state = reactive({
    type: "add",
    open: false,
    loading: false,
    typeState: {},
    dataSource: []
})

const typeStateRef = ref(null)
const drawerOpen = ref(false)
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const addType = () => {
    state.type = "add"
    state.typeState = {}
    state.open = true
}

const editType = (item) => {
    state.type = "edit"
    state.typeState = JSON.parse(JSON.stringify(item))
    state.open = true
}

const delType = (item) => {
    Modal.confirm({
        title: "确认删除",
        icon: createVNode(ExclamationCircleFilled),
        content: "确认删除当前类型吗？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            http.post("/unicard/merchant-product-type/delete", {
                id: item.id
            }).then((res) => {
                message.success(res.message)
            })
        },
        onCancel() {}
    })
}

const columns = ref([
    {
        title: "序号",
        dataIndex: "index",
        width: 100,
        customRender: (row) => `${row.index + 1}`
    },
    {
        title: "名称",
        dataIndex: "typeName",
        key: "typeName"
    },
    {
        title: "更新时间",
        dataIndex: "updateTime",
        key: "updateTime",
        sorter: true
    },
    { title: "操作", dataIndex: "operate", width: 150, fixed: "right" }
])

// 新建编辑分类
const submitbtn = () => {
    typeStateRef.value.validate().then(() => {
        state.loading = true
        const url =
            state.type == "edit"
                ? "/unicard/merchant-product-type/update"
                : "/unicard/merchant-product-type/create"
        http.post(url, state.typeState)
            .then((res) => {
                message.success(res.message)
                getList()
            })
            .finally(() => {
                state.loading = false
            })
        state.open = false
    })
}

const cancelbtn = () => {
    typeStateRef.value.resetFields()
    state.open = false
    state.typeState = {}
}

// 获取table列表
const getList = () => {
    http.post("/unicard/merchant-product-type/page", {
        ...pagination.value,
        order: state.order
    }).then((res) => {
        state.dataSource = res.data?.list
        pagination.value.total = res.data?.total
    })
}

const handleTableChange = (paginationObj, filters, sorter) => {
    if (sorter.order) {
        state.order = sorter.order === "ascend" ? "asc" : "desc"
    } else {
        delete state.order
    }
    pagination.value.pageNo = paginationObj.current
    pagination.value.pageSize = paginationObj.pageSize
    getList()
}

// 打开抽屉
const open = () => {
    drawerOpen.value = true

    getList()
}

// 关闭抽屉
const cancel = () => {
    drawerOpen.value = false
}

// 暴露给父组件的方法
defineExpose({
    open
})
</script>

<style lang="less" scoped>
.drawer-content {
    background-color: #ffffff;
    .btn-group {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 12px;
    }
}
</style>
