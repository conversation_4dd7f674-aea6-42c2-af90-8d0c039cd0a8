<template>
    <YDrawer v-model:open="drawerOpen" :title="drawerTitle" @close="cancel">
        <div class="commodity-drawer-content">
            <a-form
                :model="form"
                ref="formRef"
                layout="vertical"
                :disabled="drawerType === 'detail'"
            >
                <!-- 商品图片 -->
                <div class="form-section">
                    <div class="form-label">商品图片：</div>
                    <div class="upload-section">
                        <div class="upload-area">
                            <a-image
                                v-if="form.productImage"
                                :width="104"
                                :height="104"
                                :src="form.productImage"
                                alt="商品图片"
                                class="uploaded-image"
                            >
                                <template #previewMask>
                                    <div class="image-preview-mask">
                                        <EyeOutlined
                                            style="margin-right: 10px"
                                        />
                                        <div
                                            @click.stop="deleteImage"
                                            v-if="drawerType !== 'detail'"
                                        >
                                            <DeleteOutlined />
                                        </div>
                                    </div>
                                </template>
                            </a-image>
                            <a-upload
                                v-else-if="drawerType !== 'detail'"
                                ref="uploadRef"
                                accept=".jpg,.jpeg,.png,.bmp,.webp,.JPG,.JPEG,.PNG,.BMP,.WEBP"
                                action="/"
                                :multiple="false"
                                :show-upload-list="false"
                                :before-upload="beforeUpload"
                                class="upload-button"
                            >
                                <div class="upload-placeholder">
                                    <PlusOutlined class="upload-icon" />
                                    <div class="upload-text">上传图片</div>
                                </div>
                            </a-upload>
                            <div
                                v-else-if="
                                    drawerType === 'detail' && !form.productImage
                                "
                                class="no-image"
                            >
                                暂无图片
                            </div>
                        </div>
                        <div class="upload-tips">
                            最多上传1张，建议上传图片为400px×400px，支持图片格式为png/jpg/bmp/webp，大小不超过2M
                        </div>
                    </div>
                </div>
                <a-row :gutter="[16, 0]">
                    <a-col :span="12">
                        <!-- 商品名称 -->
                        <a-form-item
                            label="商品名称："
                            name="productName"
                            :rules="[
                                {
                                    required: true,
                                    message: '请输入商品名称',
                                    trigger: 'blur'
                                }
                            ]"
                        >
                            <a-input
                                v-model:value="form.productName"
                                placeholder="请输入"
                                show-count
                                :maxlength="50"
                                class="form-input"
                            />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <!-- 菜品条码 -->
                        <a-form-item
                            label="菜品条码："
                            name="barcode"
                            :rules="[
                                {
                                    required: true,
                                    message: '请输入菜品条码',
                                    trigger: 'blur'
                                }
                            ]"
                        >
                            <a-input
                                v-model:value="form.barcode"
                                placeholder="请输入"
                                class="form-input"
                            />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <!-- 商品分类 -->
                        <a-form-item label="商品分类：" name="typeId">
                            <a-select
                                v-model:value="form.typeId"
                                placeholder="请选择（单选）"
                                class="form-input"
                                :options="categoryOptions"
                                :fieldNames="{ label: 'typeName', value: 'id' }"
                            />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <!-- 售价 -->
                        <a-form-item
                            label="售价："
                            name="price"
                            :rules="[
                                {
                                    required: true,
                                    message: '请输入售价',
                                    trigger: 'blur'
                                },
                                {
                                    pattern: /^[0-9]+(\.[0-9]{1,2})?$/,
                                    message: '请输入正确的售价格式',
                                    trigger: 'blur'
                                }
                            ]"
                        >
                            <a-input-number
                                v-model:value="form.price"
                                placeholder="请输入"
                                :min="0"
                                :precision="2"
                                class="form-input price-input"
                                addon-after="元"
                            />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <!-- 关联商户 -->
                        <a-form-item
                            label="关联商户："
                            name="merchantId"
                            :rules="[
                                {
                                    required: true,
                                    message: '请选择',
                                    trigger: 'blur'
                                }
                            ]"
                        >
                            <a-tree-select
                                v-model:value="form.merchantId"
                                :dropdown-style="{
                                    maxHeight: '400px',
                                    overflow: 'auto'
                                }"
                                placeholder="请选择"
                                allow-clear
                                tree-default-expand-all
                                :tree-data="state.merchantList"
                                tree-node-filter-prop="label"
                                :fieldNames="{
                                    children: 'children',
                                    label: 'merchantName',
                                    value: 'id'
                                }"
                            >
                            </a-tree-select>
                        </a-form-item>
                    </a-col>

                    <a-col :span="12">
                        <!-- 商品库存 -->
                        <a-form-item
                            label="商品库存："
                            name="stock"
                            :rules="[
                                {
                                    required: true,
                                    message: '请输入商品库存',
                                    trigger: 'blur'
                                },
                                {
                                    type: 'number',
                                    min: 0,
                                    message: '库存不能小于0',
                                    trigger: 'blur'
                                }
                            ]"
                        >
                            <a-input-number
                                v-model:value="form.stock"
                                placeholder="请输入"
                                :min="0"
                                :precision="0"
                                class="form-input stock-input"
                                addon-after="件"
                            />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <!-- 商品状态 -->
                        <a-form-item
                            label="商品状态："
                            name="status"
                            :rules="[
                                {
                                    required: true,
                                    message: '请选择商品状态',
                                    trigger: 'change'
                                }
                            ]"
                        >
                            <a-radio-group v-model:value="form.status">
                                <a-radio value="online">上架</a-radio>
                                <a-radio value="offline">下架</a-radio>
                            </a-radio-group>
                        </a-form-item>
                    </a-col>
                </a-row>

                <!-- 商品描述 -->
                <a-form-item label="商品描述：" name="description">
                    <a-textarea
                        v-model:value="form.description"
                        placeholder="请输入"
                        show-count
                        :maxlength="500"
                        :rows="4"
                        class="form-textarea"
                    />
                </a-form-item>
            </a-form>
        </div>

        <template #footer>
            <a-button @click="cancel">取消</a-button>
            <a-button
                v-if="drawerType !== 'detail'"
                type="primary"
                :loading="submitLoading"
                @click="submit"
            >
                确定
            </a-button>
        </template>
    </YDrawer>
</template>

<script setup>
import { ref, computed, nextTick } from "vue"
import { message } from "ant-design-vue"
import {
    PlusOutlined,
    EyeOutlined,
    DeleteOutlined
} from "@ant-design/icons-vue"
import http from "@/utils/http"

// 定义组件的emit事件
const emit = defineEmits(["submitDrawer"])

// 响应式数据
const drawerOpen = ref(false)
const submitLoading = ref(false)
const drawerType = ref("add") // 'add' | 'edit' | 'detail'
const formRef = ref(null)
const uploadRef = ref(null)

const state = reactive({
    merchantList: []
})

// 表单数据
const form = ref({
    productName: "", // 商品名称
    barcode: "", // 菜品条码
    typeId: null, // 商品分类ID
    price: null, // 商品价格
    stock: null, // 商品库存
    status: 1, // 商品状态：1-上架，0-下架
    description: "", // 商品描述
    productImage: "", // 商品图片URL
    fileList: [] // 上传文件列表
})

// 商品分类选项
const categoryOptions = ref([])

// 计算抽屉标题
const drawerTitle = computed(() => {
    const titleMap = {
        add: "立即入库",
        edit: "编辑",
        detail: "详情"
    }
    return titleMap[drawerType.value] || "商品信息"
})

// 重置表单数据
const resetForm = () => {
    form.value = {
        productName: "", // 商品名称
        barcode: "", // 菜品条码
        typeId: null, // 商品分类ID
        price: null, // 商品价格
        stock: null, // 商品库存
        status: null, // 商品状态：1-上架，0-下架
        description: "", // 商品描述
        productImage: "", // 商品图片URL
        fileList: []
    }
    // 清除表单验证状态
    nextTick(() => {
        formRef.value?.clearValidate()
    })
}

// 图片上传前的验证
const beforeUpload = (file) => {
    // 检查文件类型
    const isValidType = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/bmp",
        "image/webp"
    ].includes(file.type)
    if (!isValidType) {
        message.error("只支持 JPG、PNG、BMP、WEBP 格式的图片!")
        return false
    }

    // 检查文件大小（2MB）
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
        message.error("图片大小不能超过 2MB!")
        return false
    }

    // 执行上传
    uploadImage(file)
    return false // 阻止默认上传行为
}

// 上传图片到服务器
const uploadImage = async (file) => {
    try {
        const response = await http.postForm("/file/cloud/common/upload", {
            file,
            folderType: "unicardProduct"
        })
        if (response.data && response.data.length > 0) {
            form.value.productImage = response.data[0].url
            form.value.fileList = response.data
            message.success("图片上传成功!")
        } else {
            form.value.fileList = []
            message.error("图片上传失败!")
        }
    } catch (error) {
        form.value.fileList = []
        message.error("图片上传失败!")
    }
}

// 删除图片
const deleteImage = () => {
    form.value.productImage = ""
    form.value.fileList = []
    message.success("图片删除成功!")
}

// 打开抽屉
const open = (type = "add", item = null) => {
    drawerType.value = type
    drawerOpen.value = true

    if (item) {
        // 编辑或详情模式，填充表单数据
        form.value = {
            ...form.value,
            ...item
        }
    } else {
        // 新建模式，重置表单
        resetForm()
    }
    getTypeList()
    getMerchantList()
}

// 关闭抽屉
const cancel = () => {
    drawerOpen.value = false
    resetForm()
}

// 提交表单
const submit = async () => {
    try {
        // 表单验证
        await formRef.value.validate()

        submitLoading.value = true

        // 构建提交数据
        const submitData = {
            ...form.value
        }

        // 模拟API调用
        // await new Promise((resolve) => setTimeout(resolve, 1000))

        // 实际开发时的API调用示例：
        const url =
            drawerType.value === "edit"
                ? "/unicard/merchant-product/update"
                : "/unicard/merchant-product/create"
        const response = await http.post(url, submitData)

        console.log("提交数据:", submitData) // 用于调试，实际开发时可删除

        message.success(
            `${drawerType.value === "edit" ? "编辑" : "新建"}商品成功!`
        )
        cancel()
        emit("submitDrawer")
    } catch (error) {
        console.error("表单验证失败:", error)
    } finally {
        submitLoading.value = false
    }
}

// 获取商品分类的列表数据
const getTypeList = () => {
    http.post("/unicard/merchant-product-type/list", {}).then((res) => {
        categoryOptions.value = res.data
    })
}

// 获取商户列表
const getMerchantList = () => {
    http.get("/unicard/merchant-product/merchantList").then((res) => {
        state.merchantList = res.data
    })
}

// 暴露给父组件的方法
defineExpose({
    open
})
</script>

<style lang="less" scoped>
.commodity-drawer-content {
    background-color: #ffffff;
    max-width: 80%;
    margin: 0 auto;

    .form-section {
        margin-bottom: 24px;

        .form-label {
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            font-family: PingFangSC-Medium;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .upload-section {
            display: flex;
            align-items: flex-start;
            gap: 12px;

            .upload-area {
                .uploaded-image {
                    border-radius: 4px;
                    border: 1px solid #d9d9d9;

                    .image-preview-mask {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: rgba(0, 0, 0, 0.5);
                        color: white;
                        cursor: pointer;
                    }
                }

                .upload-button {
                    .upload-placeholder {
                        width: 104px;
                        height: 104px;
                        background-color: #f6f6f6;
                        border: 1px dashed #d9d9d9;
                        border-radius: 4px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s;

                        &:hover {
                            border-color: var(--primary-color);
                            background-color: rgba(0, 183, 129, 0.05);
                        }

                        .upload-icon {
                            font-size: 24px;
                            color: rgba(0, 0, 0, 0.25);
                            margin-bottom: 8px;
                        }

                        .upload-text {
                            color: rgba(0, 0, 0, 0.25);
                            font-size: 14px;
                            font-family: PingFangSC-Regular;
                        }
                    }
                }

                .no-image {
                    width: 104px;
                    height: 104px;
                    background-color: #f6f6f6;
                    border: 1px solid #d9d9d9;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: rgba(0, 0, 0, 0.25);
                    font-size: 14px;
                }
            }

            .upload-tips {
                flex: 1;
                color: #595959;
                font-size: 14px;
                font-family: PingFangSC-Regular;
                line-height: 20px;
                margin-top: 42px;
            }
        }
    }

    // 表单项样式
    :deep(.ant-form-item) {
        margin-bottom: 24px;

        .ant-form-item-label {
            label {
                color: rgba(0, 0, 0, 0.85);
                font-size: 14px;
                font-weight: 500;
            }
        }

        .price-input,
        .stock-input {
            width: 100%;
            .ant-input-number-group-addon {
                background-color: #ffffff;
                color: rgba(0, 0, 0, 0.25);
                font-size: 14px;
            }
        }
    }
}

:deep(
        .ant-input-number-group > .ant-input-number:first-child,
        .ant-input-number-group .ant-input-number-group-addon:first-child
    ) {
    border-start-end-radius: 0 !important;
    border-end-end-radius: 0 !important;
}
</style>
