<template>
    <YDrawer v-model:open="drawerOpen" title="修改库存" @close="cancel">
        <div class="drawer-content">
            <div class="table_box">
                <!-- 表格 -->
                <a-table
                    :columns="columns"
                    :data-source="dataSource"
                    :pagination="false"
                >
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'balanceStock'">
                            <a-input-number
                                v-model:value="record.balanceStock"
                                placeholder="请输入"
                                :min="0"
                                :precision="0"
                                class="form-input stock-input"
                            />
                        </template>
                    </template>
                </a-table>
            </div>
        </div>

        <template #footer>
            <a-button @click="cancel">取消</a-button>
            <a-button type="primary" :loading="submitLoading" @click="submit">
                确认
            </a-button>
        </template>
    </YDrawer>
</template>

<script setup>
import { ref, computed, nextTick } from "vue"
import { message } from "ant-design-vue"
import http from "@/utils/http"

const submitLoading = ref(false)
const drawerOpen = ref(false)

const dataSource = ref([])

const columns = ref([
    {
        title: "序号",
        dataIndex: "index",
        width: 100,
        customRender: (row) => `${row.index + 1}`
    },
    { title: "商品条码", dataIndex: "barcode", key: "barcode", width: 100 },
    {
        title: "商品名称",
        dataIndex: "productName",
        key: "productName",
        width: 100
    },
    { title: "价格（元）", dataIndex: "price", key: "price", width: 100 },
    {
        title: "商品类型",
        dataIndex: "typeName",
        key: "typeName",
        width: 100
    },
    {
        title: "当前库存",
        dataIndex: "stock",
        key: "stock",
        width: 100
    },
    {
        title: "调整库存",
        dataIndex: "balanceStock",
        key: "balanceStock",
        width: 100
    }
])

// 打开抽屉
const open = (table) => {
    console.log(table, "233423")

    drawerOpen.value = true
    dataSource.value = table
}

// 关闭抽屉
const cancel = () => {
    drawerOpen.value = false
}

const submit = () => {
    submitLoading.value = true
    const productList = dataSource.value.map((item) => {
        return {
            id: item.id,
            stock: item.balanceStock
        }
    })
    // API调用
    http.post("/unicard/merchant-product/updateStock", {
        productList
    }).then((res) => {
        message.success(res.message)
        cancel()
    })
}

// 暴露给父组件的方法
defineExpose({
    open
})
</script>

<style lang="less" scoped>
.drawer-content {
    background-color: #ffffff;
}
</style>
