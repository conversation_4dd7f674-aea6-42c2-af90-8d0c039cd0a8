<template>
    <div class="page_content">
        <div class="content_box">
            <!-- 头部 -->
            <div class="card_head">
                <span class="title">销售记录</span>
            </div>
            <div class="content_page">
                <!-- 搜索组件区域 -->
                <search-form
                    style="margin-bottom: 20px"
                    v-model:formState="query"
                    :formList="formList"
                    @submit="queryList"
                    layout="horizontal"
                    @reset="resetList"
                />

                <div class="table_box">
                    <!-- 表格 -->
                    <ETable
                        :columns="columns"
                        :data-source="dataSource"
                        :paginations="state.pagination"
                        @change="handleTableChange"
                    >
                        <template #bodyCell="{ column, record, index }">
                        </template>
                    </ETable>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const state = reactive({
    pagination: {
        pageNo: 1,
        pageSize: 10
    }
})

const query = ref({})
const dataSource = ref([])

const columns = ref([
    {
        title: "序号",
        dataIndex: "index",
        width: 100,
        customRender: (row) => `${row.index + 1}`
    },
    { title: "商品条码", dataIndex: "barcode", key: "barcode" },
    {
        title: "商品名称",
        dataIndex: "productName",
        key: "productName"
    },
    { title: "售价（元）", dataIndex: "price", key: "price" },
    {
        title: "商品类型",
        dataIndex: "typeName",
        key: "typeName"
    },
    {
        title: "供应商",
        dataIndex: "supplier",
        key: "supplier"
    },
    {
        title: "购买数量",
        dataIndex: "quantity",
        key: "quantity"
    },
    {
        title: "支付渠道",
        dataIndex: "payMethod",
        key: "payMethod"
    },
    {
        title: "支付设备",
        dataIndex: "deviceName",
        key: "deviceName"
    },
    {
        title: "操作时间",
        dataIndex: "createTime",
        key: "createTime",
        sorter: true
    }
])

const formList = ref([
    {
        type: "input",
        value: "barcode",
        label: "商品条码"
    },
    {
        type: "input",
        value: "productName",
        label: "商品名称"
    },
    {
        type: "input",
        value: "supplier",
        label: "供应商"
    },
    {
        type: "select",
        value: "typeId",
        label: "商品类型",
        list: [],
        attrs: {
            fieldNames: { label: "typeName", value: "id" }
        }
    },
    {
        type: "select",
        value: "payMethod",
        label: "支付渠道",
        list: [
            { label: "微信", value: 1 },
            { label: "现金", value: 10 },
            { label: "现金", value: 11 }
        ]
    },
    {
        type: "rangePicker",
        value: ["payTimeStartDate", "payTimeEndDate"],
        label: "购买时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

// 获取商品分类的列表数据
const getTypeList = () => {
    http.post("/unicard/merchant-product-type/list", {}).then((res) => {
        formList.value[3].list = res.data
    })
}

const getList = () => {
    http.post("/unicard/merchant-product/pageOrderProduct", {
        ...state.pagination,
        ...query.value
    }).then((res) => {
        dataSource.value = res.data?.list
        state.pagination.pageNo = res.data?.pageNo
        state.pagination.pageSize = res.data?.pageSize
        state.pagination.total = res.data?.total
    })
}

function handleTableChange({ current, pageSize, total }, filters, sorter) {
    if (sorter.order) {
        query.value.order = sorter.order === "ascend" ? "asc" : "desc"
    } else {
        delete query.value.order
    }
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize

    getList()
}

function getInitList() {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

const queryList = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

const resetList = () => {
    query.value = {}
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getList()
}

onMounted(() => {
    reset()
    getTypeList()
})
</script>

<style lang="less" scoped>
.page_content {
    background: #ffffff;
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;
    .content_box {
        flex: 1;
        .card_head {
            width: 100%;
            padding: 16px 20px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-weight: 500;
                font-size: 18px;
                color: var(--text-color);
                line-height: 25px;
            }
        }
        .content_page {
            padding: 20px;
            width: 100%;

            .table_box {
                width: 100%;
            }
        }
    }
}
</style>
