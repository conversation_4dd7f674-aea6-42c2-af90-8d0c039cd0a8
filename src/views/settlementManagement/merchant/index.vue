<template>
    <div class="page_content">
        <div class="content_box">
            <!-- 头部 -->
            <div class="card_head">
                <span class="title">商户结算报表</span>
            </div>
            <div class="content_page">
                <!-- 搜索组件区域 -->
                <search-form
                    style="margin-bottom: 20px"
                    v-model:formState="query"
                    :formList="formList"
                    @submit="getInitList"
                    layout="horizontal"
                    @reset="reset"
                />

                <!-- 按钮区域 -->
                <div class="btn_group">
                    <a-button>导出</a-button>
                </div>
                <div class="table_box">
                    <!-- 表格 -->
                    <ETable
                        :bordered="true"
                        :columns="columns"
                        :data-source="dataSource"
                        :paginations="pagination"
                        @change="handleTableChange"
                    >
                        <template #bodyCell="{ column, record, index }">
                        </template>
                    </ETable>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10
})

const formList = ref([
    {
        type: "input",
        value: "deviceName",
        label: "商户名称"
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "数据日期",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const columns = ref([
    { title: "数据日期", dataIndex: "dataTime", key: "dataTime" },
    {
        title: "商户名称",
        dataIndex: "merchantName",
        key: "merchantName"
    },
    {
        title: "收款总金额",
        dataIndex: "createBy",
        key: "createBy",
        children: [
            {
                title: "总笔数",
                dataIndex: "incomeTotalNum",
                key: "incomeTotalNum"
            },
            {
                title: "总金额",
                dataIndex: "incomeTotalAmount",
                key: "incomeTotalAmount"
            }
        ]
    },
    {
        title: "刷脸支付",
        dataIndex: "createBy",
        key: "createBy",
        children: [
            {
                title: "笔数",
                dataIndex: "incomeFaceNum",
                key: "incomeFaceNum"
            },
            {
                title: "金额",
                dataIndex: "incomeFaceAmount",
                key: "incomeFaceAmount"
            }
        ]
    },
    {
        title: "刷卡支付",
        dataIndex: "createBy",
        key: "createBy",
        children: [
            {
                title: "笔数",
                dataIndex: "incomeCardNum",
                key: "incomeCardNum"
            },
            {
                title: "金额",
                dataIndex: "incomeCardAmount",
                key: "incomeCardAmount"
            }
        ]
    },
    {
        title: "退款",
        dataIndex: "createBy",
        key: "createBy",
        children: [
            {
                title: "总计",
                dataIndex: "age",
                key: "age",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "refundTotalNum",
                        key: "refundTotalNum"
                    },
                    {
                        title: "金额",
                        dataIndex: "refundTotalAmount",
                        key: "refundTotalAmount"
                    }
                ]
            },

            {
                title: "一卡通",
                dataIndex: "age",
                key: "age",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "refundUnicardNum",
                        key: "refundUnicardNum"
                    },
                    {
                        title: "金额",
                        dataIndex: "refundUnicardAmount",
                        key: "refundUnicardAmount"
                    }
                ]
            },
            {
                title: "现金",
                dataIndex: "age",
                key: "age",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "refundCashNum",
                        key: "refundCashNum"
                    },
                    {
                        title: "金额",
                        dataIndex: "refundCashAmount",
                        key: "refundCashAmount"
                    }
                ]
            },
            {
                title: "其他",
                dataIndex: "age",
                key: "age",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "refundOtherNum",
                        key: "refundOtherNum"
                    },
                    {
                        title: "金额",
                        dataIndex: "refundOtherAmount",
                        key: "refundOtherAmount"
                    }
                ]
            }
        ]
    }
])

function getList() {
    http.post("/cloud/enrollment/major/page", {
        ...pagination.value,
        ...query.value
    }).then((res) => {
        dataSource.value = res.data?.list
        pagination.value.total = res.data?.total
    })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.page_content {
    background: #ffffff;
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;
    .content_box {
        flex: 1;
        .card_head {
            width: 100%;
            padding: 16px 20px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-weight: 500;
                font-size: 18px;
                color: var(--text-color);
                line-height: 25px;
            }
        }
        .content_page {
            padding: 20px;
            width: 100%;
            .btn_group {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                margin-bottom: 16px;
            }

            .table_box {
                width: 100%;
            }
        }
    }
}
</style>
