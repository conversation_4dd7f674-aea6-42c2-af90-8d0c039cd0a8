<template>
    <!-- 金额卡片区域 -->
    <div class="card_list">
        <div class="card_item" v-for="(item, index) in state.list" :key="index">
            <div class="card_value">{{ item.value }} {{ item.unit }}</div>
            <div class="card_title">{{ item.name }}</div>
        </div>
    </div>
    <!-- 搜索组件区域 -->
    <search-form
        style="margin-bottom: 20px"
        v-model:formState="query"
        :formList="formList"
        @submit="queryList"
        layout="horizontal"
        @reset="resetList"
    />

    <div class="table_box">
        <!-- 表格 -->
        <ETable
            :columns="columns"
            :data-source="dataSource"
            :paginations="state.pagination"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex == 'operate'">
                    <a-button
                        type="link"
                        class="btn-link-color"
                        @click="handlerRefundOpen(record)"
                        >退款</a-button
                    >
                </template>
            </template>
        </ETable>
    </div>
    <!-- 退款弹窗 -->
    <refund-modal ref="refundModalRef" @submitDrawer="getInitList" />
</template>

<script setup>
import { onMounted } from "vue"

import RefundModal from "./refundModal.vue"
const refundModalRef = ref(null)
const query = ref({})
const dataSource = ref([])
const columns = ref([
    {
        title: "序号",
        dataIndex: "index",
        width: 100,
        customRender: (row) => `${row.index + 1}`
    },
    { title: "收款商户", dataIndex: "merchantName", key: "merchantName" },
    {
        title: "外部订单号",
        dataIndex: "educationalSystem",
        key: "educationalSystem"
    },
    { title: "交易流水号", dataIndex: "transactionId", key: "transactionId" },
    { title: "商品", dataIndex: "title", key: "title" },
    { title: "合计金额", dataIndex: "payAmount", key: "payAmount" },
    { title: "创建订单时间", dataIndex: "createTime", key: "createTime" },
    { title: "订单状态", dataIndex: "orderStatus", key: "orderStatus" },
    { title: "支付渠道", dataIndex: "paySource", key: "paySource" },
    { title: "支付设备", dataIndex: "deviceName", key: "deviceName" },
    { title: "支付时间", dataIndex: "payTime", key: "payTime" },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

const formList = ref([
    {
        type: "input",
        value: "orderNo",
        label: "内部订单号"
    },
    {
        type: "input",
        value: "transactionId",
        label: "交易流水号"
    },
    {
        type: "select",
        value: "machineStatus",
        label: "支付渠道",
        list: [
            { label: "微信", value: 1 },
            { label: "现金", value: 10 },
            { label: "一卡通", value: 11 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "rangePicker",
        value: ["crateTime", "endTime"],
        label: "创建订单时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    },
    {
        type: "rangePicker",
        value: ["startPayTime", "endPayTime"],
        label: "支付时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const state = reactive({
    list: [
        {
            name: "今日订单数",
            value: "0",
            unit: "笔"
        },
        {
            name: "今日收款总额",
            value: "0",
            unit: "元"
        },
        {
            name: "订单总数",
            value: "0",
            unit: "笔"
        },
        {
            name: "订单总金额",
            value: "0",
            unit: "元"
        }
    ],
    pagination: {
        pageNo: 1,
        pageSize: 10
    }
})

const getOrderStatistics = () => {
    http.post("/unicard/merchant/pay-order/order-statistics", {
        businessType: 3
    }).then((res) => {
        state.list[0].value = res.data.todayOrderNum
        state.list[1].value = res.data.todayPayAmount
        state.list[2].value = res.data.totalOrderNum
        state.list[3].value = res.data.totalPayAmount
    })
}

const paymentOrderPage = () => {
    http.post("/unicard/merchant/pay-order/payment-order-page", {
        ...state.pagination,
        ...query.value,
        businessType: 3
    }).then((res) => {
        dataSource.value = res.data?.list
        state.pagination.pageNo = res.data?.pageNo
        state.pagination.pageSize = res.data?.pageSize
        state.pagination.total = res.data?.total
    })
}

const handleTableChange = ({ current, pageSize, total }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    paymentOrderPage()
}

const handlerRefundOpen = (item) => {
    refundModalRef.value.open(item)
}

const getInitList = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    query.value = {}
    paymentOrderPage()
}

const queryList = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    paymentOrderPage()
}

const resetList = () => {
    query.value = {}
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    paymentOrderPage()
}

onMounted(() => {
    getOrderStatistics()
    getInitList()
})
</script>

<style lang="less" scoped>
.card_list {
    display: flex;
    align-items: center;

    margin-bottom: 20px;
    .card_item {
        margin-right: 12px;
        width: 110px;
        height: 70px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #e8e8e8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .card_value {
            padding-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
            color: #262626;
        }
        .card_title {
            font-weight: 400;
            font-size: 12px;
            color: #595959;
        }
    }
}
</style>
