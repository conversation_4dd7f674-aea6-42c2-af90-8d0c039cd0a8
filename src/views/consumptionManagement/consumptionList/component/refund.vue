<template>
    <!-- 搜索组件区域 -->
    <search-form
        style="margin-bottom: 20px"
        v-model:formState="query"
        :formList="formList"
        @submit="getInitList"
        layout="horizontal"
        @reset="reset"
    />

    <div class="table_box">
        <!-- 表格 -->
        <ETable
            :columns="columns"
            :data-source="dataSource"
            :paginations="pagination"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex == 'index'">{{
                    index + 1
                }}</template>
                <template v-else-if="column.dataIndex == 'operate'">
                    <a-button
                        type="link"
                        class="btn-link-color"
                        @click="addDrawerFn('detail')"
                        >详情</a-button
                    >
                    <a-button
                        type="link"
                        class="btn-link-color"
                        @click="addDrawerFn('edit')"
                        >编辑</a-button
                    >
                </template>
            </template>
        </ETable>
    </div>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router"

const router = useRouter()
const route = useRoute()

const commodityDrawerRef = ref(null)
const commodityTypeDrawerRef = ref(null)
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const columns = ref([
    { title: "序号", dataIndex: "index", width: 100 },
    { title: "退款编号", dataIndex: "majorName", key: "majorName", width: 100 },
    {
        title: "姓名",
        dataIndex: "educationalSystem",
        key: "educationalSystem",
        width: 100
    },
    { title: "卡号", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "金额", dataIndex: "createTime", key: "createTime", width: 100 },
    {
        title: "申请退款时间",
        dataIndex: "createTime",
        key: "createTime",
        width: 100
    },
    {
        title: "退款去向",
        dataIndex: "createTime",
        key: "createTime",
        width: 100
    },
    {
        title: "退款状态",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    {
        title: "退款到账时间",
        dataIndex: "createTime",
        key: "createTime",
        width: 160
    },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

// 模拟商品数据
const mockCommodityData = {
    id: 1,
    name: "香草拿铁",
    barcode: "1234567890123",
    categoryId: 1,
    price: 25.5,
    stock: 100,
    status: 1,
    description: "香浓的香草拿铁，选用优质咖啡豆，口感丝滑，香味浓郁。",
    imageUrl:
        "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
}

const formList = ref([
    {
        type: "input",
        value: "majorName",
        label: "退款编号"
    },
    {
        type: "select",
        value: "machineStatus",
        label: "类型",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "input",
        value: "majorName",
        label: "姓名"
    },
    {
        type: "input",
        value: "majorName",
        label: "卡号"
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "申请退款时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "退款到账时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const editCardRef = ref(null)
const cardInfoRef = ref(null)
const giveCardRef = ref(null)

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

// 打开抽屉
const addDrawerFn = (type, record = {}) => {
    if (type === "add") {
        // 新建商品
        commodityDrawerRef.value?.open("add")
    } else if (type === "edit") {
        // 编辑商品
        commodityDrawerRef.value?.open("edit", mockCommodityData)
    } else if (type === "detail") {
        // 商品详情
        commodityDrawerRef.value?.open("detail", mockCommodityData)
    }
}

// 处理商品抽屉提交事件
const handleSubmitDrawer = () => {
    // 刷新商品列表
    getInitList()
}
onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.table_box {
    width: 100%;
}
</style>
