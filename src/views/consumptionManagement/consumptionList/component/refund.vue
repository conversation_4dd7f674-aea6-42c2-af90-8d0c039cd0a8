<template>
    <!-- 搜索组件区域 -->
    <search-form
        style="margin-bottom: 20px"
        v-model:formState="query"
        :formList="formList"
        @submit="getInitList"
        layout="horizontal"
        @reset="reset"
    />

    <div class="table_box">
        <!-- 表格 -->
        <ETable
            :columns="columns"
            :data-source="dataSource"
            :paginations="pagination"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex == 'operate'">
                    <a-button
                        type="link"
                        class="btn-link-color"
                        @click="reviewInfoModalRef.open(record)"
                        >详情</a-button
                    >
                    <a-button
                        type="link"
                        class="btn-link-color"
                        @click="reviewModalRef.open(record)"
                        >退款审核</a-button
                    >
                </template>
            </template>
        </ETable>
    </div>
    <review-modal ref="reviewModalRef" @submitDrawer="getInitList" />
    <ReviewInfoModal ref="reviewInfoModalRef"></ReviewInfoModal>
</template>

<script setup>
import { onMounted } from "vue"
import ReviewModal from "./reviewModal.vue"
import ReviewInfoModal from "./reviewInfoModal.vue"
const reviewModalRef = ref(null)
const reviewInfoModalRef = ref(null)
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10
})
const columns = ref([
    {
        title: "序号",
        dataIndex: "index",
        width: 100,
        customRender: (row) => `${row.index + 1}`
    },
    { title: "退款编号", dataIndex: "refundNo", key: "refundNo" },
    {
        title: "姓名",
        dataIndex: "orderUserName",
        key: "orderUserName"
    },
    {
        title: "卡号",
        dataIndex: "orderUserName",
        key: "orderUserName",
        width: 100
    },
    {
        title: "金额",
        dataIndex: "refundAmount",
        key: "refundAmount"
    },
    {
        title: "申请退款时间",
        dataIndex: "createTime",
        key: "createTime"
    },
    {
        title: "退款去向",
        dataIndex: "refundDestination",
        key: "refundDestination"
    },
    {
        title: "退款状态",
        dataIndex: "orderStatusName",
        key: "orderStatusName"
    },
    {
        title: "退款到账时间",
        dataIndex: "refundTime",
        key: "refundTime"
    },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

const formList = ref([
    {
        type: "input",
        value: "refundNo",
        label: "退款编号"
    },
    {
        type: "input",
        value: "itemName",
        label: "类型"
    },
    {
        type: "input",
        value: "orderUserName",
        label: "姓名"
    },
    {
        type: "input",
        value: "orderUserName",
        label: "卡号"
    },
    {
        type: "rangePicker",
        value: ["crateTime", "endTime"],
        label: "申请退款时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    },
    {
        type: "rangePicker",
        value: ["startRefundTime", "endRefundTime"],
        label: "退款到账时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const getList = () => {
    http.post("/unicard/merchant/refund-order/refund-order-page", {
        ...pagination.value,
        ...query.value,
        businessType: 3
    }).then((res) => {
        dataSource.value = res.data?.list
        pagination.value.pageNo = res.data?.pageNo
        pagination.value.pageSize = res.data?.pageSize
        pagination.value.total = res.data?.total
    })
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

function reset() {
    query.value = {}
    getInitList()
}

onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.table_box {
    width: 100%;
}
</style>
