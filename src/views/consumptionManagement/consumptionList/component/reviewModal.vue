<template>
    <YModal
        v-model:open="state.open"
        title="退款审核"
        width="700px"
        :bodyStyle="{ padding: '24px' }"
        @cancel="handleRefundCancel"
    >
        <a-form
            class="refund-form"
            :model="state.refundForm"
            ref="formRef"
            layout="vertical"
        >
            <a-form-item
                name="refundAmount"
                label="本次可退款金额："
                :rules="[{ required: true, message: '请输入退款金额!' }]"
            >
                <a-input
                    :disabled="true"
                    v-model:value.trim="state.refundForm.refundAmount"
                    addon-after="元"
                >
                </a-input>
            </a-form-item>

            <div class="refund-reason">
                <div class="refund-reason-label">退款原因：</div>
                <div class="refund-reason-text">
                    {{ state.refundForm.refundReason }}
                </div>
            </div>

            <!-- 同意拒绝 -->
            <a-form-item
                style="margin-bottom: 24px"
                name="auditStatus"
                label=""
                :rules="[{ required: true, message: '请选择审核结果!' }]"
            >
                <a-radio-group v-model:value="state.refundForm.auditStatus">
                    <a-radio :value="1">同意</a-radio>
                    <a-radio :value="2">拒绝</a-radio>
                </a-radio-group>
            </a-form-item>
            <!-- 审核意见 -->
            <a-form-item
                name="auditRemarks"
                label="审核意见："
                :rules="[{ required: true, message: '请输入审核意见!' }]"
            >
                <a-textarea
                    v-model:value.trim="state.refundForm.auditRemarks"
                    placeholder="请输入审核意见"
                    show-count
                    :maxlength="100"
                    :auto-size="{ minRows: 6, maxRows: 5 }"
                />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button key="back" @click="handleRefundCancel">取消</a-button>
            <a-button
                key="submit"
                type="primary"
                :loading="state.refundLoading"
                @click="handleRefundOk"
                >确定</a-button
            >
        </template>
    </YModal>
</template>

<script setup>
import { reactive, ref } from "vue"

const emit = defineEmits(["submitDrawer"])

const state = reactive({
    open: false,
    refundLoading: false,
    refundForm: {
        auditStatus: 1
    }
})

const formRef = ref(null)

function handleRefundOk() {
    formRef.value.validate().then(() => {
        state.refundLoading = true
        http.post(
            "/unicard/merchant/refund-order/refund-audit",
            state.refundForm
        )
            .then(({ msg }) => {
                message.success(msg)
                handleRefundCancel()
                emit("submitDrawer")
            })
            .finally(() => {
                state.refundLoading = false
            })
    })
}

function handleRefundCancel() {
    state.open = false
    state.refundForm.auditStatus = 1
    state.refundForm.auditRemarks = ""
}

defineExpose({
    open(item) {
        state.open = true
        state.refundForm.refundReason = item.refundReason // 退款原因
        state.refundForm.id = item.orderId // 退款id
        state.refundForm.refundAmount = item.refundAmount // 退款金额
    },
    close() {
        state.open = false
    }
})
</script>

<style lang="less" scoped>
.refund-list {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    // 每行3列
    .refund-item {
        width: 33%;
        margin-bottom: 24px;
        display: flex;

        .refund-item-title {
            font-size: 14px;
            font-weight: 400;
            color: #000000a6;
        }

        .refund-item-money {
            // 字数过多省略
            width: max-content;
            display: inline-block;
            width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: bottom;
            flex: 1;
        }
    }
}

.refund-reason {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;
    margin-top: 24px;

    .refund-reason-label {
        // width: 100px;
        font-size: 14px;
        font-weight: 400;
        color: #000000a6;
    }

    .refund-reason-text {
        flex: 1;
        font-size: 14px;
        font-weight: 400;
        color: #000000a6;
    }
}

:deep(
        .ant-input-group > .ant-input:first-child,
        .ant-input-group .ant-input-group-addon:first-child
    ) {
    border-start-end-radius: 0 !important;
    border-end-end-radius: 0 !important;
}
</style>
