<template>
    <YModal
        v-model:open="state.open"
        title="退款申请"
        width="700px"
        :bodyStyle="{ padding: '24px' }"
        @cancel="handleRefundCancel"
    >
        <ul class="refund-list">
            <li
                class="refund-item"
                v-for="item in refundApplication"
                :key="item.key"
            >
                <span class="refund-item-title">{{ item.title }}</span>
                <span class="refund-item-money">{{
                    state.refundForm[item.key] || "-"
                }}</span>
            </li>
        </ul>

        <a-form
            class="refund-form"
            :model="state.refundForm"
            ref="formRef"
            layout="vertical"
        >
            <a-form-item
                name="refundAmount"
                label="本次可退款金额："
                :rules="[{ required: true, message: '请输入退款金额!' }]"
            >
                <a-input v-model:value.trim="state.refundForm.refundAmount">
                </a-input>
            </a-form-item>
            <a-form-item
                name="refundReason"
                label="退款原因："
                :rules="[{ required: true, message: '请输入退款原因!' }]"
            >
                <a-textarea
                    v-model:value.trim="state.refundForm.refundReason"
                    placeholder="请输入退款原因"
                    show-count
                    :maxlength="100"
                    :auto-size="{ minRows: 6, maxRows: 5 }"
                />
            </a-form-item>
        </a-form>
        <template #footer>
            <a-button key="back" @click="handleRefundCancel">取消</a-button>
            <a-button
                key="submit"
                type="primary"
                :loading="state.refundLoading"
                @click="handleRefundOk"
                >确定</a-button
            >
        </template>
    </YModal>
</template>

<script setup>
import { reactive, ref } from "vue"

const emit = defineEmits(["submitDrawer"])

const state = reactive({
    open: false,
    refundLoading: false,
    refundForm: {}
})

const refundApplication = ref([
    { title: "项目", key: "orderNo" },
    { title: "姓名", key: "name" },
    { title: "学号", key: "cardNo" },
    { title: "卡号", key: "amount" },
    { title: "充值金额", key: "time" }
])

const formRef = ref(null)

function handleRefundOk() {
    formRef.value.validate().then(() => {
        state.refundLoading = true
        http.post(
            "/unicard/merchant/pay-order/refund-request",
            state.refundForm
        )
            .then(({ msg }) => {
                message.success(msg)
                handleRefundCancel()
                emit("submitDrawer")
            })
            .finally(() => {
                state.refundLoading = false
            })
    })
}

function handleRefundCancel() {
    state.open = false
    state.refundForm.refundReason = ""
    state.refundForm.refundAmount = ""
}

defineExpose({
    open(item) {
        state.open = true
        state.refundForm.orderId = item.orderId
    },
    close() {
        state.open = false
    }
})
</script>

<style lang="less" scoped>
.refund-list {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    // 每行3列
    .refund-item {
        width: 33%;
        margin-bottom: 24px;
        display: flex;

        .refund-item-title {
            font-size: 14px;
            font-weight: 400;
            color: #000000a6;
        }

        .refund-item-money {
            // 字数过多省略
            width: max-content;
            display: inline-block;
            width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: bottom;
            flex: 1;
        }
    }
}
</style>
