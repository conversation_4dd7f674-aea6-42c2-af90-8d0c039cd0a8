<template>
    <YModal
        v-model:open="state.open"
        title="退款详情"
        width="700px"
        :bodyStyle="{ padding: '24px' }"
        @cancel="handleRefundCancel"
        :footer="null"
    >
        <div class="refund-reason">
            <div class="refund-reason-label">本次可退款金额：</div>
            <div class="refund-reason-text">
                {{ state.info.refundAmount }}
            </div>
        </div>

        <div class="refund-reason">
            <div class="refund-reason-label">退款原因：</div>
            <div class="refund-reason-text">
                {{ state.info.refundReason }}
            </div>
        </div>

        <div>
            <a-radio-group
                :disabled="true"
                v-model:value="state.info.auditStatus"
            >
                <a-radio :value="1">同意</a-radio>
                <a-radio :value="2">拒绝</a-radio>
            </a-radio-group>
        </div>
        <div class="refund-reason">
            <div class="refund-reason-label">审核意见：</div>
            <div class="refund-reason-text">
                {{ state.info.auditRemarks }}
            </div>
        </div>
    </YModal>
</template>

<script setup>
import { reactive, ref } from "vue"

const emit = defineEmits(["submitDrawer"])

const state = reactive({
    open: false,
    info: {},
    detailId: ""
})

// 获取退款详情
const getDetail = () => {
    http.post("/unicard/merchant/refund-order/detail", {
        id: state.detailId
    }).then((res) => {
        state.info = res.data
    })
}

defineExpose({
    open(item) {
        state.detailId = item.id
        state.open = true
        getDetail()
    }
})
</script>

<style lang="less" scoped>
.refund-list {
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    // 每行3列
    .refund-item {
        width: 33%;
        margin-bottom: 24px;
        display: flex;

        .refund-item-title {
            font-size: 14px;
            font-weight: 400;
            color: #000000a6;
        }

        .refund-item-money {
            // 字数过多省略
            width: max-content;
            display: inline-block;
            width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: bottom;
            flex: 1;
        }
    }
}

.refund-reason {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24px;
    margin-top: 24px;

    .refund-reason-label {
        // width: 100px;
        font-size: 14px;
        font-weight: 400;
        color: #000000a6;
    }

    .refund-reason-text {
        flex: 1;
        font-size: 14px;
        font-weight: 400;
        color: #000000a6;
    }
}

:deep(
        .ant-input-group > .ant-input:first-child,
        .ant-input-group .ant-input-group-addon:first-child
    ) {
    border-start-end-radius: 0 !important;
    border-end-end-radius: 0 !important;
}
</style>
