<template>
    <div class="merchant_settlement">
        <div class="header">商户结算报表</div>
        <div class="content">
            <search-form v-model:formState="query" :formList="formList" @submit="getInitList" layout="horizontal" @reset="reset" />
            <div class="btn_group">
                <a-button>导出</a-button>
            </div>

            <ETable bordered :columns="columns" :scroll="{ x: 1500 }" :data-source="dataSource" :paginations="pagination" @change="handleTableChange"> </ETable>
        </div>
    </div>
</template>

<script setup>
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const formList = ref([
    {
        type: "input",
        value: "majorName",
        label: "商户名称"
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "数据日期",
        attrs: {
            placeholder: ["开始日期", "结束日期"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const columns = [
    {
        title: "数据日期",
        dataIndex: "name",
        key: "name"
    },
    {
        title: "商户名称",
        dataIndex: "age",
        key: "age"
    },
    {
        title: "收款总额",
        children: [
            {
                title: "总笔数",
                dataIndex: "ag1e",
                key: "ag1e"
            },
            {
                title: "总金额",
                dataIndex: "ag12",
                key: "ag12"
            }
        ]
    },
    {
        title: "刷脸支付",
        children: [
            {
                title: "笔数",
                dataIndex: "ag1e",
                key: "ag1e"
            },
            {
                title: "金额",
                dataIndex: "ag12",
                key: "ag12"
            }
        ]
    },
    {
        title: "刷卡支付",
        children: [
            {
                title: "笔数",
                dataIndex: "ag1e",
                key: "ag1e"
            },
            {
                title: "金额",
                dataIndex: "ag12",
                key: "ag12"
            }
        ]
    },
    {
        title: "退款订单",
        children: [
            {
                title: "总计",
                children: [
                    {
                        title: "总笔数",
                        dataIndex: "ag1e",
                        key: "ag1e"
                    },
                    {
                        title: "总金额",
                        dataIndex: "ag12",
                        key: "ag12"
                    }
                ]
            },
            {
                title: "一卡通",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "ag1e",
                        key: "ag1e"
                    },
                    {
                        title: "金额",
                        dataIndex: "ag12",
                        key: "ag12"
                    }
                ]
            },
            {
                title: "现金",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "ag1e",
                        key: "ag1e"
                    },
                    {
                        title: "金额",
                        dataIndex: "ag12",
                        key: "ag12"
                    }
                ]
            },
            {
                title: "其他",
                children: [
                    {
                        title: "笔数",
                        dataIndex: "ag1e",
                        key: "ag1e"
                    },
                    {
                        title: "金额",
                        dataIndex: "ag12",
                        key: "ag12"
                    }
                ]
            }
        ]
    }
]

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, name: "1", ag12: "121", ag1e: "dqdw" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function reset() {
    query.value = {}
    getInitList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.merchant_settlement {
    .header {
        padding: 18px 20px;
        font-weight: 500;
        font-size: 18px;
        color: #262626;
        line-height: 25px;
        border-bottom: 1px solid #d9d9d9;
    }
    .content {
        padding: 20px;

        .btn_group {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 16px;
        }
    }
}
</style>
