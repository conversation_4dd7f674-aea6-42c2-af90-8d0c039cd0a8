<template>
    <a-form :model="formState" :labelCol="labelCol" :layout="layout">
        <a-row :gutter="[24, 16]">
            <a-col
                v-for="item in formList"
                :key="item.value"
                v-bind="item.attrs || {}"
                :xs="20"
                :sm="16"
                :md="12"
                :lg="8"
                :xl="6"
                :xxl="4"
                :style="{
                    minWidth: item.type === 'rangePicker' ? '400px' : null
                }"
            >
                <a-form-item
                    :label="item.label"
                    v-bind="validateInfos[item.value]"
                    :labelCol="item.labelCol"
                    :wrapperCol="item.wrapperCol"
                >
                    <slot
                        :name="item.value"
                        v-bind="item"
                        v-if="item.type === 'slot'"
                    ></slot>
                    <template v-else>
                        <!-- 输入框 -->
                        <a-input
                            v-if="item.type === 'input'"
                            v-model:value="formState[item.value]"
                            :placeholder="`请输入${item.label}`"
                            v-bind="item.attrs || {}"
                            allowClear
                        />
                        <!-- 数字输入框 -->
                        <a-input-number
                            v-if="item.type === 'inputNumber'"
                            v-model:value="formState[item.value]"
                            :placeholder="`请输入${item.label}`"
                            v-bind="item.attrs || {}"
                            allowClear
                        />
                        <!-- 文本框 -->
                        <a-textarea
                            v-if="item.type === 'textarea'"
                            v-model:value="formState[item.value]"
                            :placeholder="`请输入${item.label}`"
                            v-bind="item.attrs || {}"
                            allowClear
                        />
                        <!-- 选择器 -->
                        <a-select
                            v-else-if="item.type === 'select'"
                            v-model:value="formState[item.value]"
                            :options="item.list"
                            :placeholder="`请选择${item.label}`"
                            v-bind="item.attrs || {}"
                            allowClear
                        ></a-select>
                        <!-- 日期选择器 -->
                        <a-date-picker
                            v-else-if="item.type === 'datePicker'"
                            v-model:value="formState[item.value]"
                            v-bind="item.attrs || {}"
                            allowClear
                        />
                        <!-- 日期选择区间 -->
                        <!-- -->
                        <RangePicker
                            v-else-if="item.type === 'rangePicker'"
                            v-model:startTime="formState[item.value[0]]"
                            v-model:endTime="formState[item.value[1]]"
                        >
                        </RangePicker>
                        <!-- 时间选择区间 -->
                        <a-time-picker
                            v-else-if="item.type === 'timePicker'"
                            v-model:value="formState[item.value]"
                            v-bind="item.attrs || {}"
                            allowClear
                        />
                        <!-- 省市区级联 -->
                        <!-- <a-cascader
                            v-else-if="item.type === 'cascader'"
                            v-model:value="formState[item.value]"
                            allowClear
                            :placeholder="`请选择${item.label}`"
                            v-bind="item.attrs || {}"
                            :options="item.list"
                        /> -->

                        <!-- 加时间的日期选择区间 (需要虚拟的voidValue key值)-->
                        <!-- <a-range-picker
                            v-else-if="item.type === 'rangePickerTime'"
                            v-model:value="formState[item.voidValue]"
                            v-bind="item.attrs || {}"
                            allowClear
                            @change="rangePickerTimeChange($event, item)"
                        /> -->
                        <a-range-picker
                            v-else-if="item.type === 'rangePickers'"
                            v-model:value="formState[item.value]"
                            v-bind="item.attrs || {}"
                            allowClear
                            @change="rangePickerTimeChange($event, item)"
                        />
                    </template>
                </a-form-item>
            </a-col>
            <a-col
                class="search-form-footer"
                v-if="showBtn"
                :xs="20"
                :sm="16"
                :md="12"
                :lg="8"
                :xl="6"
                :xxl="4"
            >
                <a-form-item class="footer-item">
                    <div class="footer-item-btn">
                        <slot name="footerBtn" v-if="footerBtn"></slot>
                        <template v-else>
                            <a-button
                                type="primary"
                                @click="submit"
                                style="margin-right: 8px"
                            >
                                <template #icon>
                                    <SearchOutlined />
                                </template>
                                查询
                            </a-button>

                            <a-button @click="reset">
                                <template #icon>
                                    <ReloadOutlined />
                                </template>
                                重置
                            </a-button>
                        </template>
                    </div>
                </a-form-item>
            </a-col>
        </a-row>
    </a-form>
</template>
<script setup>
import { Form } from "ant-design-vue"
const useForm = Form.useForm
const prop = defineProps({
    layout: {
        type: String,
        default: "inline"
        // 'horizontal'|'vertical'|'inline'
    },
    formState: {
        type: Object,
        default: () => {
            return {}
        }
    },
    formList: {
        type: Array,
        default: () => {
            return []
        }
    },
    rules: {
        type: Object,
        default: () => {
            return {}
        }
    },
    showBtn: {
        type: Boolean,
        default: true
    },
    labelCol: {
        type: Object,
        default: () => {
            return {}
        }
    },
    btnSpan: {
        type: Object,
        default: () => {
            return {}
        }
    },
    // 页脚
    footerBtn: {
        type: Boolean,
        default: false
    }
})

// const span = computed(() => {
//     return (item) => {
//         // $ 类型为rangePicker且没设置span，默认值给5
//         if (!item.span && item.type === "rangePicker") {
//             return 5
//         }
//         return item.span
//     }
// })

const rules = computed(() => {
    const rules = prop.formList.reduce((pre, cur) => {
        if (cur.rules) {
            pre[cur.value] = cur.rules
        }
        return pre
    }, {})
    return Object.assign(rules, prop.rules)
})

// 白名单 (不进行初始化的类型白名单)
const whitelist = ["slot", "rangePicker"]

watch(
    () => prop.formList,
    () => {
        prop.formList.forEach((i) => {
            if (!prop.formState[i.value] && !whitelist.includes(i.type)) {
                prop.formState[i.value] = null
            }
        })
    },
    {
        immediate: true,
        deep: true
    }
)

const { resetFields, validate, validateInfos } = useForm(prop.formState, rules)

const emit = defineEmits(["submit", "reset", "barCode"])

const submit = () => {
    console.log(prop.formState, rules, "prop.formState, rules")

    return validate()
        .then(() => {
            emit("submit")
            return true
        })
        .catch(() => {
            return false
        })
}

const reset = () => {
    resetFields()
    // 原因是点击详情后，表单数据需要被重置，但是表单数据没有被重置，所以需要手动重置表单数据
    prop.formList.forEach((i) => {
        if (prop.formState[i.value]) {
            prop.formState[i.value] = null
        }
        // 判断i.value 是否是数组
        if (Array.isArray(i.voidValue)) {
            prop.formState[i.value] = null
            prop.formState[i.voidValue[0]] = null
            prop.formState[i.voidValue[1]] = null
        }
    })
    emit("reset")
}

// 日期时间选择区间
const rangePickerTimeChange = (event, item) => {
    prop.formState[item.voidValue[0]] = event ? event[0] : null
    prop.formState[item.voidValue[1]] = event ? event[1] : null
}
defineExpose({ submit, reset, Form })
</script>
<style lang="less" scoped>
:deep(.ant-form-item) {
    margin-bottom: 0px !important;
}

.search-form-footer {
    .footer-item-btn {
        display: flex;
        align-items: center;
    }
}
</style>
