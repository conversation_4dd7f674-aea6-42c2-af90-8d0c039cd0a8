<template>
    <div class="setting">
        <a-popover trigger="click" placement="rightBottom">
            <template #content>
                <div class="feature">
                    <a-checkbox
                        v-model:checked="state.checkAll"
                        :indeterminate="state.indeterminate"
                        @change="onCheckAllChange"
                    >
                        全选
                    </a-checkbox>
                    <a-button type="link" class="btn-link-color" @click="reset"
                        >重置</a-button
                    >
                </div>
                <a-divider mt-10 mb-10 />
                <a-checkbox-group
                    class="columns-group"
                    v-model:value="state.checkedList"
                    @change="changeItems"
                >
                    <VueDraggableNext
                        handle=".draggable-dom"
                        :sort="true"
                        :list="state.list"
                        @change="onOrderList"
                    >
                        <div
                            v-for="item in state.list"
                            :key="item.key"
                            class="group-items"
                        >
                            <a-checkbox :value="item.key">
                                <div class="draggable-container">
                                    <img
                                        class="draggable-dom"
                                        src="@/assets/images/btn_draggable.png"
                                        alt="btn_draggable"
                                    />
                                    <span>{{ item.name }}</span>
                                </div>
                            </a-checkbox>
                        </div>
                    </VueDraggableNext>
                </a-checkbox-group>
            </template>
            <div flex flex-items-center class="btn-link-color">
                <i class="iconfont icon-icon-shezhi" mt-1></i>
                <span pl-4>设置</span>
            </div>
        </a-popover>
    </div>
</template>

<script setup>
import { VueDraggableNext } from "vue-draggable-next"
import Throttle from "@/utils/Throttle"
const throttle = new Throttle()
const emit = defineEmits(["update"])
const props = defineProps({
    list: {
        type: Array,
        default: () => {
            return []
        }
    }
})

// *********************
// Hooks Function
// *********************

const state = reactive({
    checkedList: [],
    indeterminate: false,
    checkAll: false,
    list: []
})

const allChecked = computed(() => state.list.map((i) => i.key))

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

// 监听list顺序改变
const onOrderList = () => {
    changeItems(state.checkedList)
}

const onCheckAllChange = (e) => {
    const checked = e.target.checked ? allChecked.value : []
    changeItems(checked)
}

// 改变items
const changeItems = (list, action = "update") => {
    throttle.start(
        (val) => {
            state.indeterminate = !!val.length && val.length < state.list.length
            state.checkAll = val.length === state.list.length
            state.checkedList = val
            // 更新数据
            const updateData = {}
            state.list.forEach((item, index) => {
                const { key, name, originOrder } = item
                updateData[key] = {
                    name,
                    originOrder,
                    checked: val.includes(key),
                    order: index
                }
            })
            if (action === "update") {
                emit("update", updateData)
            }
        },
        list,
        800
    )
}

const reset = () => {
    // 恢复起始位置
    state.list.sort((pre, next) => pre.originOrder - next.originOrder)
    changeItems(allChecked.value)
}

// *********************
// Watch Function
// *********************

watch(
    () => props.list,
    (val) => {
        state.list = val
        const checked = val
            .map((item) => {
                if (item.checked) {
                    return item.key
                }
            })
            .filter(Boolean)
        changeItems(checked, "watch")
    },
    {
        immediate: true,
        deep: true
    }
)
</script>

<style lang="less" scoped>
.feature {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.columns-group {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    max-height: 350px;
    margin-right: -3px;
    overflow: hidden auto;

    :deep(.ant-checkbox-wrapper) {
        margin-bottom: 8px;
    }

    :deep(.ant-checkbox-wrapper-checked) {
        color: var(--primary-color);
    }
}

.group-items {
    display: flex;
    width: 100%;

    :deep(.ant-checkbox + span) {
        padding-inline-start: 0;
        padding-inline-end: 0;
        flex: 1;
    }

    .ant-checkbox-wrapper {
        display: flex;
        width: 100%;
        flex-direction: row-reverse;
        padding-right: 10px;
    }

    .draggable-container {
        display: flex;
        align-items: center;
        margin-right: 20px;
    }

    :deep(.ant-checkbox-disabled) {
        background: #b2e9d9;

        .ant-checkbox-inner {
            border-color: #b2e9d9;
        }

        .ant-checkbox-inner:after {
            border-color: #fff;
        }
    }
}

.draggable-container {
    img {
        width: 16px;
        cursor: move;
    }

    span {
        font-size: 14px;
        font-weight: 400;
        color: var(--primary-color);
    }
}
</style>
