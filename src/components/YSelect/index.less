.mSelect-wrap {
    display: flex;
    height: 540px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);

    .section {
        display: flex;
        flex-direction: column;
        width: 50%;
        padding: 16px 0;

        &:last-child {
            border-left: 1px solid #f0f0f0;
        }
    }

    .ant-input-search {
        display: block;
        width: auto;
        margin: 0 16px;
    }

    .select-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        border-top: 1px solid #f0f0f0;
        margin-top: 16px;
        padding: 16px 16px 0;
        overflow: hidden;
    }

    .ant-breadcrumb {
        margin-top: 16px;

        span:last-child {
            pointer-events: none;
        }
    }

    .tabs {
        :deep(.ant-radio-group) {
            display: flex;
            text-align: center;
        }

        :deep(.ant-radio-button-wrapper) {
            flex: 1;

            &:first-child {
                border-top-left-radius: 40px;
                border-bottom-left-radius: 40px;
            }

            &:last-child {
                border-top-right-radius: 40px;
                border-bottom-right-radius: 40px;
            }
        }
    }

    .structures {
        flex: 1;
        overflow-y: auto;
        margin-right: -10px;

        .row {
            display: flex;
            align-items: center;
            padding: 6px 8px;
            border-radius: 4px;
            margin-bottom: 6px;

            &:hover,
            &:active {
                background-color: #f6f6f6;
            }
        }

        .check {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .check-visible {
            pointer-events: none;

            :deep(.ant-radio),
            :deep(.ant-checkbox) {
                visibility: hidden;
            }
        }

        .cnt {
            flex: 1;
            max-width: 170px;
            margin-left: 8px;
        }

        :deep(.ant-radio + span),
        :deep(.ant-checkbox + span) {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .sub {
            color: #999;
        }

        .more {
            font-size: 14px;
            color: var(--primary-color);
            line-height: 16px;
            padding-left: 12px;
            border-left: 1px solid #d9d9d9;
            margin-left: auto;
            cursor: pointer;
            user-select: none;
        }
    }

    :deep(.ant-avatar) {
        font-size: 14px;
        background: var(--primary-color);
    }

    :deep(.ant-avatar-image) {
        background: transparent;
    }

    .selected-hd {
        display: flex;
        align-items: center;
        padding: 0 12px;

        .count {
            font-size: 12px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.85);
            line-height: 17px;
            margin-left: 8px;
        }

        .btn-clear {
            color: var(--primary-color);
            margin-left: auto;
            cursor: pointer;
            user-select: none;
        }
    }

    .selected-bd {
        flex: 1;
        padding: 15px 12px 0;
        overflow-y: auto;
    }

    .selected-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .selected-item {
            display: flex;
            align-items: center;
            padding: 4px;
            background: #f6f6f6;
            border-radius: 4px;
        }

        .cnt {
            margin-left: 4px;
        }

        .sub {
            color: #999;
        }

        .icon-del {
            width: 14px;
            height: 14px;
            margin-left: 8px;
            background: url('/image/icon-del.png') no-repeat center;
            cursor: pointer;
            user-select: none;
        }
    }
}

.empty {
    text-align: center;
    padding-top: 80px;

    .empty-img {
        width: 180px;
        height: 180px;
    }
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
}
