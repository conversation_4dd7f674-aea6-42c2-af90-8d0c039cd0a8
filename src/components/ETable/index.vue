<template>
    <a-table rowKey="id" v-bind="$attrs" :pagination="pagination">
        <template #emptyText>
            <slot name="emptyText">
                <Empty :emptyStyle="{ marginBottom: '65px' }"></Empty>
            </slot>
        </template>
        <template #bodyCell="{ column, text, record, index }">
            <Tooltip
                v-if="slotsName"
                :maxWidth="column.width"
                :title="text"
            ></Tooltip>
            <template v-for="item in Object.keys(slots)" :key="item">
                <slot
                    :name="item"
                    :column="column"
                    :text="text"
                    :record="record"
                    :index="index"
                />
                <!-- <slot v-if="['bodyCell'].includes(item)" :name="item" :column :text :record :index />
                <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip> -->
            </template>
        </template>
    </a-table>
</template>
<script setup>
const props = defineProps({
    paginations: {
        type: Object,
        default: () => {
            return {
                total: 0,
                pageNo: 1,
                pageSize: 10
            }
        }
    }
})
const pagination = ref({
    showQuickJumper: true,
    showLessItems: true,
    showSizeChanger: true,
    pageSizeOptions: ["10", "20", "30", "40", "100"],
    showTotal: (total) => `共 ${total} 条`
})
watch(
    () => props.paginations,
    (val) => {
        pagination.value = {
            ...pagination.value,
            ...val,
            current: val.pageNo
        }
    },
    {
        deep: true
    }
)
const slots = useSlots()
// 判断是否有插槽
const slotsName = computed(() => {
    return (
        Object.keys(slots).length === 1 && Object.keys(slots)[0] === "default"
    )
})
</script>
<style lang="less" scoped></style>
