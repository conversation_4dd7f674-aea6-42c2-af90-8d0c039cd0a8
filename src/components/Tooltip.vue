<template>
    <div :style="customWidth">
        <a-tooltip
            class="_tooltip"
            :title="props.title"
            :overlayStyle="state.overlayStyle"
            @mouseenter="showToolTip"
        >
            <span class="tooltip_span">{{ tooltipTitle }}</span>
        </a-tooltip>
    </div>
</template>

<script setup>
import { reactive, computed } from "vue"
const props = defineProps({
    title: {
        type: [String, Number, Object],
        default: ""
    },
    maxWidth: {
        type: [Number, String],
        default: 0
    }
})

const customWidth = computed(() => {
    if (
        typeof props.maxWidth === "string" &&
        props.maxWidth.indexOf("px") !== -1
    ) {
        return {
            width: props.maxWidth
        }
    }
    return {
        width: props.maxWidth ? props.maxWidth + "px" : "initial"
    }
})

const state = reactive({
    overlayStyle: {
        color: "#000",
        maxWidth: props.maxWidth ? props.maxWidth + "px" : "initial"
    }
})
// 控制Tooltip显隐
const showToolTip = (e) => {
    if (e.target.clientWidth >= e.target.scrollWidth) {
        e.target.style.pointerEvents = "none"
        // 阻止鼠标事件
    }
}
const tooltipTitle = computed(() => {
    const { title } = props
    if (title !== "" && title !== null && title !== undefined) {
        return title
    }
    return "--"
})
</script>

<style scoped lang="less">
._tooltip {
    min-height: 22px;
}

.tooltip_span {
    display: block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

:deep(.ant-tooltip) {
    max-width: max-content !important;

    .ant-tooltip-inner {
        width: auto !important;
    }
}
</style>
