<template>
    <a-modal
        v-model:open="visible"
        :title="modalTitle"
        :width="500"
        :maskClosable="false"
        :closable="false"
        :destroyOnClose="true"
        @cancel="handleCancel"
        @ok="handleConfirm"
        :confirmLoading="loading"
    >
        <a-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
        >
            <!-- 手机号码输入框 -->
            <a-form-item label="手机号码" name="phone">
                <a-input
                    v-model:value="formData.phone"
                    placeholder="请输入手机号码"
                    :maxlength="11"
                    allow-clear
                />
            </a-form-item>

            <!-- 验证码输入框 -->
            <a-form-item label="验证码" name="verifyCode">
                <a-input-group compact>
                    <a-input
                        v-model:value="formData.verifyCode"
                        placeholder="请输入验证码"
                        :maxlength="6"
                        style="width: 60%"
                        allow-clear
                    />
                    <a-button
                        :disabled="countdown > 0 || !isPhoneValid"
                        :loading="sendingCode"
                        @click="sendVerifyCode"
                        style="width: 40%"
                    >
                        {{
                            countdown > 0
                                ? `${countdown}s后重新发送`
                                : "发送验证码"
                        }}
                    </a-button>
                </a-input-group>
            </a-form-item>

            <!-- 新密码输入框 -->
            <a-form-item label="新密码" name="newPassword">
                <a-input-password
                    v-model:value="formData.newPassword"
                    placeholder="请输入新密码"
                    :maxlength="20"
                    allow-clear
                />
                <div class="password-tip">
                    请输入包含字母、数字、特殊字符，长度为8-20个字符的密码！
                </div>
            </a-form-item>

            <!-- 确认新密码输入框 -->
            <a-form-item label="确认新密码" name="confirmPassword">
                <a-input-password
                    v-model:value="formData.confirmPassword"
                    placeholder="请再次输入新密码"
                    :maxlength="20"
                    allow-clear
                />
            </a-form-item>
        </a-form>
    </a-modal>
</template>

<script setup>
import { reactive, ref, computed, watch, onUnmounted } from "vue"
import { message, Modal } from "ant-design-vue"
import { ExclamationCircleOutlined } from "@ant-design/icons-vue"
import { createVNode } from "vue"
import http from "@/utils/http"
import RSA from "@/utils/rsa.js"
import useStore from "@/store"
import { useRouter } from "vue-router"

// 定义组件属性
const props = defineProps({
    // 弹窗显示状态
    open: {
        type: Boolean,
        default: false
    },
    // 使用场景：'initial' - 初始密码修改, 'forget' - 忘记密码, 'change' - 修改密码
    scene: {
        type: String,
        default: "initial",
        validator: (value) => ["initial", "forget", "change"].includes(value)
    }
})

// 定义事件
const emit = defineEmits(["update:open", "success", "cancel"])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)
const formRef = ref()
let countdownTimer = null

// 获取store和router
const { user } = useStore()
const router = useRouter()

// 表单数据
const formData = reactive({
    phone: "",
    verifyCode: "",
    newPassword: "",
    confirmPassword: ""
})

// 计算属性
const modalTitle = computed(() => {
    switch (props.scene) {
        case "initial":
            return "修改初始密码"
        case "forget":
            return "忘记密码"
        case "change":
            return "修改密码"
        default:
            return "修改密码"
    }
})

// 手机号码格式验证
const isPhoneValid = computed(() => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(formData.phone)
})

// 密码强度验证函数
const validatePassword = (rule, value) => {
    if (!value) {
        return Promise.reject(new Error("请输入新密码"))
    }

    // 密码规则：包含字母、数字、特殊字符，长度为8-20个字符
    const hasLetter = /[a-zA-Z]/.test(value)
    const hasNumber = /\d/.test(value)
    const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)
    const isValidLength = value.length >= 8 && value.length <= 20

    if (!isValidLength) {
        return Promise.reject(new Error("密码长度必须为8-20个字符"))
    }

    if (!hasLetter || !hasNumber || !hasSpecial) {
        return Promise.reject(new Error("密码必须包含字母、数字和特殊字符"))
    }

    return Promise.resolve()
}

// 确认密码验证函数
const validateConfirmPassword = (rule, value) => {
    if (!value) {
        return Promise.reject(new Error("请确认新密码"))
    }

    if (value !== formData.newPassword) {
        return Promise.reject(new Error("两次输入的密码不一致"))
    }

    return Promise.resolve()
}

// 表单验证规则
const rules = reactive({
    phone: [
        { required: true, message: "请输入手机号码" },
        { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码" }
    ],
    verifyCode: [
        { required: true, message: "请输入验证码" },
        { pattern: /^\d{6}$/, message: "验证码为6位数字" }
    ],
    newPassword: [{ required: true, validator: validatePassword }],
    confirmPassword: [{ required: true, validator: validateConfirmPassword }]
})

// 发送验证码
const sendVerifyCode = async () => {
    if (!isPhoneValid.value) {
        message.error("请输入正确的手机号码")
        return
    }

    try {
        sendingCode.value = true

        // 调用发送验证码接口
        await http.post("/unicard/admin-user/sendVerifyCode", {
            phone: formData.phone,
            type: "password_reset" // 密码重置类型
        })

        message.success("验证码发送成功")

        // 开始倒计时
        startCountdown()
    } catch (error) {
        console.error("发送验证码失败:", error)
        message.error(error.message || "发送验证码失败")
    } finally {
        sendingCode.value = false
    }
}

// 开始倒计时
const startCountdown = () => {
    countdown.value = 60
    countdownTimer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
            clearInterval(countdownTimer)
            countdownTimer = null
        }
    }, 1000)
}

// 清除所有缓存
const clearAllCache = () => {
    // 清除localStorage
    localStorage.clear()

    // 清除store中的用户信息
    user.setToken("")
    user.setupInfo({})

    // 清除sessionStorage
    sessionStorage.clear()
}

// 处理取消操作
const handleCancel = () => {
    if (props.scene === "initial") {
        // 初始密码修改场景，取消时清除所有缓存并跳转到登录页
        Modal.confirm({
            title: "提示",
            icon: createVNode(ExclamationCircleOutlined),
            content:
                "检测到您是首次登录，必须修改初始密码才能继续使用系统。取消修改将退出登录，是否确认？",
            okText: "确定退出",
            cancelText: "继续修改",
            onOk() {
                clearAllCache()
                message.info("已退出登录")
                router.replace("/user/login")
                closeModal()
            }
        })
    } else {
        // 忘记密码和修改密码场景，直接关闭弹窗
        closeModal()
    }
}

// 处理确认操作
const handleConfirm = async () => {
    try {
        // 表单验证
        await formRef.value.validate()

        loading.value = true

        // 调用修改密码接口
        const requestData = {
            phone: formData.phone,
            verifyCode: formData.verifyCode,
            newPwd: formData.newPassword,
            confirmPwd: formData.confirmPassword
        }

        await http.post("/unicard/admin-user/updateNewPassword", {
            paramEncipher: RSA.encrypt(JSON.stringify(requestData))
        })

        message.success("密码修改成功")

        if (props.scene === "initial") {
            // 初始密码修改成功后，使用新密码自动登录
            message.info("密码修改成功，正在为您自动登录...")

            // 延迟一下再自动登录，让用户看到成功提示
            setTimeout(async () => {
                try {
                    // 使用新密码自动登录
                    const loginData = {
                        grant_type: "password",
                        client_id: "yide-partner",
                        client_secret: "yide1234567",
                        username: formData.phone, // 使用手机号作为用户名
                        password: formData.newPassword
                    }

                    const loginRes = await http.postForm("/auth/oauth/token", {
                        paramEncipher: RSA.encrypt(JSON.stringify(loginData))
                    })

                    // 更新token
                    const token = loginRes.data.accessToken
                    localStorage.setItem("token", token)
                    user.setToken(token)

                    // 获取用户信息
                    const userInfoRes = await http.get(
                        "/unicard/admin-user/login"
                    )
                    user.setupInfo(userInfoRes.data)

                    // 跳转到首页
                    router.replace("/")
                } catch (error) {
                    console.error("自动登录失败:", error)
                    message.error("自动登录失败，请手动登录")
                    clearAllCache()
                    router.replace("/user/login")
                }
            }, 1500)
        } else {
            // 忘记密码和修改密码场景，清除缓存并跳转到登录页
            message.info("密码修改成功，请重新登录")
            setTimeout(() => {
                clearAllCache()
                router.replace("/user/login")
            }, 1500)
        }

        closeModal()
        emit("success")
    } catch (error) {
        console.error("修改密码失败:", error)
        message.error(error.message || "修改密码失败")
    } finally {
        loading.value = false
    }
}

// 关闭弹窗
const closeModal = () => {
    visible.value = false
    emit("update:open", false)
    emit("cancel")

    // 重置表单
    resetForm()
}

// 重置表单
const resetForm = () => {
    formData.phone = ""
    formData.verifyCode = ""
    formData.newPassword = ""
    formData.confirmPassword = ""

    // 清除验证状态
    if (formRef.value) {
        formRef.value.clearValidate()
    }

    // 清除倒计时
    if (countdownTimer) {
        clearInterval(countdownTimer)
        countdownTimer = null
    }
    countdown.value = 0
}

// 监听props.open变化
watch(
    () => props.open,
    (newVal) => {
        visible.value = newVal
        if (!newVal) {
            resetForm()
        }
    },
    { immediate: true }
)

// 组件卸载时清理定时器
onUnmounted(() => {
    if (countdownTimer) {
        clearInterval(countdownTimer)
    }
})
</script>

<style lang="less" scoped>
.password-tip {
    font-size: 12px;
    color: #999;
    margin-top: 4px;
    line-height: 1.4;
}

:deep(.ant-input-group) {
    display: flex;
}

:deep(.ant-input-group .ant-input) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

:deep(.ant-input-group .ant-btn) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
}
</style>
