# 修改密码弹窗组件 (ChangePasswordModal)

## 功能描述

这是一个通用的修改密码弹窗组件，支持三种使用场景：

1. **初始密码修改** (`scene="initial"`) - 用户首次登录时强制修改初始密码
2. **忘记密码** (`scene="forget"`) - 用户忘记密码时重置密码
3. **修改密码** (`scene="change"`) - 用户主动修改密码

## 组件特性

- ✅ 手机号码输入和格式验证
- ✅ 短信验证码发送和60秒倒计时
- ✅ 密码强度验证（包含字母、数字、特殊字符，长度8-20位）
- ✅ 确认密码一致性验证
- ✅ 表单验证和错误提示
- ✅ 不同场景下的不同行为逻辑
- ✅ 自动登录功能（初始密码修改场景）
- ✅ 缓存清理和登出功能

## 使用方法

### 1. 导入组件

```vue
<script setup>
import ChangePasswordModal from '@/components/ChangePasswordModal/index.vue'
</script>
```

### 2. 在模板中使用

```vue
<template>
    <!-- 修改密码弹窗组件 -->
    <ChangePasswordModal
        v-model:open="changePasswordModalVisible"
        :scene="passwordModalScene"
        @success="onPasswordChangeSuccess"
        @cancel="onPasswordChangeCancel"
    />
</template>
```

### 3. 在脚本中定义响应式数据和方法

```vue
<script setup>
import { ref } from 'vue'

// 弹窗显示状态
const changePasswordModalVisible = ref(false)

// 使用场景：'initial' | 'forget' | 'change'
const passwordModalScene = ref('change')

// 显示修改密码弹窗
const showChangePasswordModal = () => {
    passwordModalScene.value = 'change'
    changePasswordModalVisible.value = true
}

// 显示忘记密码弹窗
const showForgetPasswordModal = () => {
    passwordModalScene.value = 'forget'
    changePasswordModalVisible.value = true
}

// 密码修改成功回调
const onPasswordChangeSuccess = () => {
    console.log('密码修改成功')
}

// 密码修改取消回调
const onPasswordChangeCancel = () => {
    console.log('密码修改取消')
}
</script>
```

## 组件属性 (Props)

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `open` | `Boolean` | `false` | 弹窗显示状态，支持 v-model |
| `scene` | `String` | `'initial'` | 使用场景：`'initial'` \| `'forget'` \| `'change'` |

## 组件事件 (Events)

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:open` | `boolean` | 弹窗显示状态变化时触发 |
| `success` | - | 密码修改成功时触发 |
| `cancel` | - | 取消修改密码时触发 |

## 不同场景的行为差异

### 初始密码修改 (`scene="initial"`)

- **取消操作**：显示确认对话框，确认后清除所有缓存并跳转到登录页
- **确认操作**：修改密码成功后使用新密码自动登录并跳转到首页
- **弹窗特性**：不可通过点击遮罩层关闭，不显示关闭按钮

### 忘记密码 (`scene="forget"`)

- **取消操作**：直接关闭弹窗
- **确认操作**：修改密码成功后清除缓存并跳转到登录页，提示用户重新登录

### 修改密码 (`scene="change"`)

- **取消操作**：直接关闭弹窗
- **确认操作**：修改密码成功后清除缓存并跳转到登录页，提示用户重新登录

## 密码规则

密码必须满足以下条件：
- 包含字母（大写或小写）
- 包含数字
- 包含特殊字符 `!@#$%^&*()_+-=[]{};':"\\|,.<>/?`
- 长度为8-20个字符

## API 接口

组件内部调用以下接口：

1. **发送验证码**：`POST /unicard/admin-user/sendVerifyCode`
2. **修改密码**：`POST /unicard/admin-user/updateNewPassword`
3. **登录**：`POST /auth/oauth/token` (仅初始密码修改场景)
4. **获取用户信息**：`GET /unicard/admin-user/login` (仅初始密码修改场景)

## 注意事项

1. 组件依赖 `@/utils/http` 和 `@/utils/rsa.js` 工具
2. 组件依赖 `@/store` 状态管理
3. 组件使用 `ant-design-vue` 组件库
4. 密码数据会通过 RSA 加密后传输
5. 初始密码修改场景下，用户必须完成密码修改才能继续使用系统

## 示例

完整的使用示例可以参考：
- `src/views/user/login.vue` - 登录页面中的初始密码修改
- `src/views/user/profile.vue` - 用户设置页面中的密码修改和忘记密码
