{"name": "one-card-pc", "version": "1.0.0", "description": "一卡通管理系统", "scripts": {"dev": "vite", "build:dev": "vite build --mode development", "build:uat": "vite build --mode uat", "build:uatrelease": "vite build --mode uatrelease", "build:prod": "vite build --mode production", "preview": "vite preview", "docs:dev": "vitepress dev docs --config /docs/vite.config.js", "docs:build": "vitepress build docs --config /docs/vite.config.js", "docs:serve": "vitepress serve docs --config /docs/vite.config.js", "lint": "eslint --fix --ext .js,.ts,.tsx,.vue src", "format": "prettier --write \"src/**/*.js\" \"src/**/*.ts\" \"src/**/*.tsx\" \"src/**/*.vue\"", "prepare": "husky install", "commit": "git cz", "release": "standard-version && git push --follow-tags", "lint:style": "stylelint --fix \"src/**/*.{css,less,vue}\"", "preinstall": "npx only-allow pnpm"}, "browserslist": "ie >= 11", "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "animate.css": "^4.1.1", "ant-design-vue": "^4.2.6", "axios": "^1.3.4", "color": "^5.0.0", "dayjs": "^1.11.13", "nprogress": "^0.2.0", "pinia": "^2.0.32", "pinia-plugin-persistedstate": "^3.1.0", "qs": "^6.11.1", "secure-ls": "^1.2.6", "typescript": "^4.9.5", "vite": "^4.5.14", "vue": "^3.5.16", "vue-router": "^4.1.6", "web-vitals": "^3.1.1", "wow.js": "^1.2.2", "encryptlong": "^3.1.4"}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "5.41.0", "@typescript-eslint/parser": "^5.52.0", "@vitejs/plugin-legacy": "^4.0.1", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/eslint-config-prettier": "^7.0.0", "autoprefixer": "^10.4.14", "babel-eslint": "^10.1.0", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.0.0", "eslint": "^8.34.0", "eslint-formatter-pretty": "^4.1.0", "eslint-plugin-json": "^3.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.9.0", "git-cz": "^4.9.0", "husky": "^8.0.3", "less": "^4.3.0", "picocolors": "^1.0.0", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.8.4", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-external-globals": "^0.7.1", "rollup-plugin-visualizer": "^5.9.0", "standard-version": "^9.5.0", "stylelint": "^15.2.0", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended-less": "^1.0.4", "stylelint-config-standard": "^30.0.1", "stylelint-less": "^1.0.6", "stylelint-order": "^6.0.3", "unplugin-auto-import": "^0.15.0", "unplugin-vue-components": "^0.24.0", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-inspect": "^0.7.28", "vite-plugin-mkcert": "^1.14.0", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-stylelint": "^4.3.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-svgr": "^2.4.0", "vitepress": "1.0.0-beta.2", "vue-global-api": "^0.4.1"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}}, "lint-staged": {"*.{js,ts,vue}": ["eslint --fix --max-warnings=0"]}}