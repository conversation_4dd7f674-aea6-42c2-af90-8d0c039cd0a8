/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    404: typeof import('./../src/components/404.vue')['default']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    ABackTop: typeof import('ant-design-vue/es')['BackTop']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACardMeta: typeof import('ant-design-vue/es')['CardMeta']
    ACascader: typeof import('ant-design-vue/es')['Cascader']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AImage: typeof import('ant-design-vue/es')['Image']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputGroup: typeof import('ant-design-vue/es')['InputGroup']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    ARow: typeof import('ant-design-vue/es')['Row']
    ArrowLeftOutlined: typeof import('@ant-design/icons-vue')['ArrowLeftOutlined']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimePicker: typeof import('ant-design-vue/es')['TimePicker']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    BugOutlined: typeof import('@ant-design/icons-vue')['BugOutlined']
    CaretDownOutlined: typeof import('@ant-design/icons-vue')['CaretDownOutlined']
    ChangePasswordModal: typeof import('./../src/components/ChangePasswordModal/index.vue')['default']
    copy: typeof import('./../src/components/YSelect/index copy.vue')['default']
    DempTree: typeof import('./../src/components/DempTree/index.vue')['default']
    DempTreeData: typeof import('./../src/components/DempTree/data.ts')['default']
    Empty: typeof import('./../src/components/Empty.vue')['default']
    ETable: typeof import('./../src/components/ETable/index.vue')['default']
    HomeOutlined: typeof import('@ant-design/icons-vue')['HomeOutlined']
    KeyOutlined: typeof import('@ant-design/icons-vue')['KeyOutlined']
    MenuUnfoldOutlined: typeof import('@ant-design/icons-vue')['MenuUnfoldOutlined']
    MyIframe: typeof import('./../src/components/myIframe/index.vue')['default']
    MyTable: typeof import('./../src/components/myTable/index.vue')['default']
    NoAuth: typeof import('./../src/components/NoAuth.vue')['default']
    Pagination: typeof import('./../src/components/Pagination.vue')['default']
    PlusOutlined: typeof import('@ant-design/icons-vue')['PlusOutlined']
    PoweroffOutlined: typeof import('@ant-design/icons-vue')['PoweroffOutlined']
    RangePicker: typeof import('./../src/components/RangePicker.vue')['default']
    ReloadOutlined: typeof import('@ant-design/icons-vue')['ReloadOutlined']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RouteTabs: typeof import('./../src/components/routeTabs/index.vue')['default']
    SearchForm: typeof import('./../src/components/searchForm/index.vue')['default']
    SearchFormFilterCriteria: typeof import('./../src/components/searchForm/filterCriteria.vue')['default']
    SearchFormSearchSetting: typeof import('./../src/components/searchForm/searchSetting.vue')['default']
    SearchOutlined: typeof import('@ant-design/icons-vue')['SearchOutlined']
    SettingOutlined: typeof import('@ant-design/icons-vue')['SettingOutlined']
    SvgIcon: typeof import('./../src/components/svgIcon/index.tsx')['default']
    Tooltip: typeof import('./../src/components/Tooltip.vue')['default']
    UserOutlined: typeof import('@ant-design/icons-vue')['UserOutlined']
    VerticalAlignTopOutlined: typeof import('@ant-design/icons-vue')['VerticalAlignTopOutlined']
    WebOpen: typeof import('./../src/components/webOpen/index.vue')['default']
    YDrawer: typeof import('./../src/components/YDrawer/index.vue')['default']
    YImport: typeof import('./../src/components/YImport/index.vue')['default']
    YModal: typeof import('./../src/components/YModal/index.vue')['default']
    YSelect: typeof import('./../src/components/YSelect/index.vue')['default']
    YSelectIndex11: typeof import('./../src/components/YSelect/index11.vue')['default']
  }
}
